import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
    ToggleButton, Avatar, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip, Card, ListItemIcon, ToggleButtonGroup,
    TableContainer as MuiTableContainer, CardContent, Divider
} from '@mui/material';
import { createTheme, ThemeProvider, styled } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, <PERSON>Off,
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dns, <PERSON>, Storage
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';


// --- MOCK DATA & CONSTANTS ---
const SERVER_TYPES = { HCL: 'HCL Server', CUSTOMER: 'Customer Server' };
const TENANT_TYPES = { SINGLE: 'Single-Tenant', MULTI: 'Multi-Tenant' };
const INFRASTRUCTURE_TYPES = { CLOUD: 'Cloud Provider', DC: 'Own Data Center' };
const CLOUD_PROVIDERS = { AWS: 'Amazon AWS', AZURE: 'Microsoft Azure', GCP: 'Google Cloud' };
const SERVER_STATUSES = { Online: 'Online', Offline: 'Offline', Maintenance: 'Maintenance' };
const ENVIRONMENTS = { Production: 'Production', Staging: 'Staging', Development: 'Development' };
const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const initialServers = [
    { id: 1, customer: 'Innovate Corp', serverType: 'HCL', tenantType: 'MULTI', status: 'Online', environment: 'Production', infrastructure: { type: '', details: {} } },
    { id: 2, customer: 'Synergy Ltd', serverType: 'CUSTOMER', tenantType: 'SINGLE', status: 'Online', environment: 'Production', infrastructure: { type: 'CLOUD', provider: 'AWS', host: 'prod.aws.synergy.com', port: '5432', username: 'synergy_prod', dbName: 'syn_db_prod' } },
    { id: 3, customer: 'Quantum Solutions', serverType: 'CUSTOMER', tenantType: 'SINGLE', status: 'Maintenance', environment: 'Staging', infrastructure: { type: 'CLOUD', provider: 'AZURE', host: 'staging.azure.quantum.com', port: '1433', username: 'quantum_stage', dbName: 'quan_db_stage' } },
    { id: 4, customer: 'Apex Industries', serverType: 'CUSTOMER', tenantType: 'SINGLE', status: 'Offline', environment: 'Production', infrastructure: { type: 'DC', host: 'dc.apex.local', port: '1521', username: 'apex_admin', dbName: 'apex_prod_db' } },
    { id: 5, customer: 'NextGen Retail', serverType: 'HCL', tenantType: 'MULTI', status: 'Online', environment: 'Production', infrastructure: { type: '', details: {} } },
    { id: 6, customer: 'Data Systems', serverType: 'CUSTOMER', tenantType: 'SINGLE', status: 'Online', environment: 'Development', infrastructure: { type: 'CLOUD', provider: 'GCP', host: 'dev.gcp.datasys.io', port: '3306', username: 'dev_user', dbName: 'dev_main' } },
];

const mockCustomers = [...new Set(initialServers.map(s => s.customer))];

const ALL_COLUMNS = [
    { key: 'customer', label: 'Customer', type: 'string', groupable: true },
    { key: 'status', label: 'Status', type: 'string', groupable: true },
    { key: 'serverType', label: 'Server Type', type: 'string', groupable: true },
    { key: 'tenantType', label: 'Tenant Model', type: 'string', groupable: true },
    { key: 'environment', label: 'Environment', type: 'string', groupable: true },
    { key: 'infrastructure.type', label: 'Infrastructure', type: 'string', groupable: true },
    { key: 'infrastructure.provider', label: 'Provider/Location', type: 'string', groupable: true },
];

const SERVER_STATUS_COLORS = { Online: 'success', Offline: 'error', Maintenance: 'warning' };


// --- UI COMPONENTS (Defined before use) ---
const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <List disablePadding>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <AdminComponents.StyledScheduleIcon fontSize="small" />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <ListItemText
                            primary={
                                <AdminComponents.ActivityLogTextContainer>
                                    <Typography variant="body2" component="span" color="text.secondary">
                                        <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                        {' '}{log.action}{' '}
                                        {log.target && <Typography component="span" fontWeight="bold">{log.target}</Typography>}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {log.timestamp}
                                    </Typography>
                                </AdminComponents.ActivityLogTextContainer>
                            }
                        />
                    </AdminComponents.ActivityLogListItem>
                ))}
            </List>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const ActionButtons = ({ server, onView, onEdit, onDelete, isCondensed }) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = (event) => {
        event.stopPropagation();
        setAnchorEl(null);
    };

    const handleAction = (action, event) => {
        handleClose(event);
        action();
    }

    if (isCondensed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton id="long-button" onClick={handleClick} size="small"><MoreVert /></IconButton>
                <Menu id="long-menu" anchorEl={anchorEl} open={open} onClose={handleClose}>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onView(server, false), e)}><Visibility fontSize="small" /> View</AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onEdit(server, true), e)}><Edit fontSize="small" /> Edit</AdminComponents.ActionMenuItem>
                    <AdminComponents.ErrorMenuItem onClick={(e) => handleAction(() => onDelete([server.id]), e)}><Delete fontSize="small" /> Delete</AdminComponents.ErrorMenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(server, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onEdit(server, true)} title="Edit"><Edit fontSize="small" /></IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([server.id])} title="Delete"><Delete fontSize="small" /></IconButton>
        </Box>
    );
};

const ServerCard = ({ server, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(server.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ server, onView, onEdit, onDelete, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{server.customer}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{SERVER_TYPES[server.serverType]}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: server.status }} label={server.status} size="small" color={SERVER_STATUS_COLORS[server.status] || 'default'}/>
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Tenant Model:</strong> {TENANT_TYPES[server.tenantType]}</Typography>
            <Typography variant="body2"><strong>Environment:</strong> {server.environment}</Typography>
            {server.infrastructure?.type && <Typography variant="body2" noWrap><strong>Infra:</strong> {INFRASTRUCTURE_TYPES[server.infrastructure.type]}</Typography>}
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const ServerCompactCard = ({ server, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(server.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ server, onView, onEdit, onDelete, isCondensed: true }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{server.customer}</Typography>
                <Typography variant="caption" color="text.secondary">{SERVER_TYPES[server.serverType]}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{TENANT_TYPES[server.tenantType]}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: server.status }} label={server.status} size="small" color={SERVER_STATUS_COLORS[server.status] || 'default'} />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const ServerListItem = ({ server, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(server.id)} />
            <Box>
                <Typography fontWeight="bold">{server.customer}</Typography>
                <Typography variant="body2" color="text.secondary">{server.environment}</Typography>
            </Box>
            <Typography variant="body2">{SERVER_TYPES[server.serverType]}</Typography>
            <Typography variant="body2">{TENANT_TYPES[server.tenantType]}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: server.status }} label={server.status} size="small" color={SERVER_STATUS_COLORS[server.status] || 'default'} />
            <AdminComponents.ListItemActions>
                 <ActionButtons {...{ server, onView, onEdit, onDelete, isCondensed }} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);


const ServerTable = ({ servers, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, onDelete, onEdit, onView, isCondensed }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (serv, colKey) => {
        if (colKey === 'status') return <AdminComponents.StatusBadge ownerState={{ status: serv.status }} label={serv.status} size="small" color={SERVER_STATUS_COLORS[serv.status] || 'default'} />;
        
        const value = colKey.includes('.') ? colKey.split('.').reduce((o, i) => o?.[i], serv) : serv[colKey];
        
        if (colKey === 'serverType') return SERVER_TYPES[value] || '-';
        if (colKey === 'tenantType') return TENANT_TYPES[value] || '-';
        if (colKey === 'infrastructure.type') return INFRASTRUCTURE_TYPES[value] || '-';
        if (colKey === 'infrastructure.provider') return CLOUD_PROVIDERS[value] || (serv.infrastructure.type === 'DC' ? 'On-Premise' : '-');


        if (colKey === 'customer') {
            return (
                <AdminComponents.ClickableTypography component="span" onClick={() => onRowClick(serv)}>
                    {value}
                </AdminComponents.ClickableTypography>
            );
        }
        return value || '-';
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };
    
    const renderGroupedRows = (data, keys, level = 0) => {
        if (!keys.length || !data.length) {
            return data.map(serv => (
                <TableRow key={serv.id} hover selected={selectedId === serv.id} onClick={() => onRowClick(serv)}>
                    <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(serv.id)} onChange={() => onSelectOne(serv.id)} onClick={e => e.stopPropagation()} /></TableCell>
                    {columnOrder.map(colKey => (
                        <AdminComponents.ContentTableCell key={colKey}>
                            {renderCellContent(serv, colKey)}
                        </AdminComponents.ContentTableCell>
                    ))}
                    <AdminComponents.ActionTableCell align="center">
                        <ActionButtons {...{ server: serv, onView, onEdit, onDelete, isCondensed }} />
                    </AdminComponents.ActionTableCell>
                </TableRow>
            ));
        }

        const currentKey = keys[0];
        const remainingKeys = keys.slice(1);
        const groupLabel = ALL_COLUMNS.find(c => c.key === currentKey)?.label;

        const grouped = data.reduce((acc, item) => {
            const groupValue = String(item[currentKey] || 'N/A');
            if (!acc[groupValue]) acc[groupValue] = [];
            acc[groupValue].push(item);
            return acc;
        }, {});

        return Object.entries(grouped).map(([groupValue, items]) => (
            <React.Fragment key={`${level}-${groupValue}`}>
                <AdminComponents.GroupHeaderRow>
                    <AdminComponents.GroupHeaderCell level={level} colSpan={columnOrder.length + 2}>
                        <strong>{groupLabel}:</strong> {groupValue} ({items.length})
                    </AdminComponents.GroupHeaderCell>
                </AdminComponents.GroupHeaderRow>
                {renderGroupedRows(items, remainingKeys, level + 1)}
            </React.Fragment>
        ));
    };


    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < servers.length} checked={servers.length > 0 && selectedIds.length === servers.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renderGroupedRows(servers, groupByKeys)}
                </TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

const ServerDetailsDialog = ({ open, onClose, serverData, isAdding, onSave }) => {
    const [formData, setFormData] = useState({});

    useEffect(() => {
        const initialFormData = {
            customer: '',
            serverType: 'HCL',
            tenantType: 'MULTI',
            status: 'Online',
            environment: 'Production',
            infrastructure: { type: '', provider: '', host: '', port: '', username: '', dbName: '' }
        };

        if (isAdding) {
            setFormData(initialFormData);
        } else {
            setFormData(serverData || initialFormData);
        }
    }, [serverData, isAdding]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        const newFormData = { ...formData, [name]: value };

        if (name === 'serverType') {
            if (value === 'HCL') {
                newFormData.tenantType = 'MULTI';
                newFormData.infrastructure = { type: '', provider: '', host: '', port: '', username: '', dbName: '' };
            } else {
                newFormData.tenantType = 'SINGLE';
                newFormData.infrastructure = { type: 'CLOUD', provider: 'AWS', host: '', port: '', username: '', dbName: '' };
            }
        }
        
        if (name === 'tenantType' && value === 'MULTI') {
             newFormData.infrastructure = { type: '', provider: '', host: '', port: '', username: '', dbName: '' };
        }
        
        setFormData(newFormData);
    };

    const handleInfraChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            infrastructure: { ...prev.infrastructure, [name]: value }
        }));
    };
    
    const handleSave = () => {
        onSave(formData);
        onClose();
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
            <DialogTitle>{isAdding ? 'Add New Server' : 'Edit Server Details'}</DialogTitle>
            <DialogContent>
                <AdminComponents.FormContainer>
                    <TextField name="customer" label="Customer Name" value={formData.customer || ''} onChange={handleChange} />
                    
                    <FormControl fullWidth>
                        <InputLabel>Server Type</InputLabel>
                        <Select name="serverType" value={formData.serverType || 'HCL'} label="Server Type" onChange={handleChange}>
                            {Object.entries(SERVER_TYPES).map(([key, value]) => <MenuItem key={key} value={key}>{value}</MenuItem>)}
                        </Select>
                    </FormControl>

                    {formData.serverType === 'CUSTOMER' && (
                        <FormControl fullWidth>
                            <InputLabel>Tenant Model</InputLabel>
                            <Select name="tenantType" value={formData.tenantType || 'SINGLE'} label="Tenant Model" onChange={handleChange}>
                                {Object.entries(TENANT_TYPES).map(([key, value]) => <MenuItem key={key} value={key}>{value}</MenuItem>)}
                            </Select>
                        </FormControl>
                    )}

                    {formData.serverType === 'CUSTOMER' && formData.tenantType === 'SINGLE' && (
                        <>
                            <FormControl fullWidth>
                                <InputLabel>Infrastructure Type</InputLabel>
                                <Select name="type" value={formData.infrastructure?.type || 'CLOUD'} label="Infrastructure Type" onChange={handleInfraChange}>
                                    {Object.entries(INFRASTRUCTURE_TYPES).map(([key, value]) => <MenuItem key={key} value={key}>{value}</MenuItem>)}
                                </Select>
                            </FormControl>

                            {formData.infrastructure?.type === 'CLOUD' && (
                                <FormControl fullWidth>
                                    <InputLabel>Cloud Provider</InputLabel>
                                    <Select name="provider" value={formData.infrastructure?.provider || 'AWS'} label="Cloud Provider" onChange={handleInfraChange}>
                                       {Object.entries(CLOUD_PROVIDERS).map(([key, value]) => <MenuItem key={key} value={key}>{value}</MenuItem>)}
                                    </Select>
                                </FormControl>
                            )}
                            
                            <TextField name="host" label="Host" value={formData.infrastructure?.host || ''} onChange={handleInfraChange} />
                            <TextField name="port" label="Port" value={formData.infrastructure?.port || ''} onChange={handleInfraChange} />
                            <TextField name="username" label="Username" value={formData.infrastructure?.username || ''} onChange={handleInfraChange} />
                            <TextField name="dbName" label="DB Name" value={formData.infrastructure?.dbName || ''} onChange={handleInfraChange} />
                        </>
                    )}
                     <FormControl fullWidth>
                        <InputLabel>Environment</InputLabel>
                        <Select name="environment" value={formData.environment || 'Production'} label="Environment" onChange={handleChange}>
                            {Object.entries(ENVIRONMENTS).map(([key, value]) => <MenuItem key={key} value={key}>{value}</MenuItem>)}
                        </Select>
                    </FormControl>
                    <FormControl fullWidth>
                        <InputLabel>Status</InputLabel>
                        <Select name="status" value={formData.status || 'Online'} label="Status" onChange={handleChange}>
                             {Object.entries(SERVER_STATUSES).map(([key, value]) => <MenuItem key={key} value={key}>{value}</MenuItem>)}
                        </Select>
                    </FormControl>
                </AdminComponents.FormContainer>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={handleSave} variant="contained">Save</Button>
            </DialogActions>
        </Dialog>
    );
};

const ServerGraph = ({ servers, chartType, isVisible }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);
    
    const chartConfig = useMemo(() => {
        if (!servers.length) return null;
        
        let labels, data, title, type, backgroundColors;
        
        switch(chartType) {
            case 'bar':
                const statusCounts = servers.reduce((acc, s) => { acc[s.status] = (acc[s.status] || 0) + 1; return acc; }, {});
                labels = Object.keys(statusCounts);
                data = Object.values(statusCounts);
                title = 'Servers by Status';
                type = 'bar';
                backgroundColors = theme.palette.chart.backgrounds;
                break;
            case 'line':
                const envCounts = servers.reduce((acc, s) => { acc[s.environment] = (acc[s.environment] || 0) + 1; return acc; }, {});
                labels = Object.keys(envCounts);
                data = Object.values(envCounts);
                title = 'Servers by Environment';
                type = 'line';
                backgroundColors = theme.palette.secondary.main;
                break;
            case 'pie':
                const typeCounts = servers.reduce((acc, s) => { acc[SERVER_TYPES[s.serverType]] = (acc[SERVER_TYPES[s.serverType]] || 0) + 1; return acc; }, {});
                labels = Object.keys(typeCounts);
                data = Object.values(typeCounts);
                title = 'Servers by Type';
                type = 'pie';
                backgroundColors = theme.palette.chart.backgrounds;
                break;
            case 'doughnut':
            default:
                const tenantCounts = servers.reduce((acc, s) => { acc[TENANT_TYPES[s.tenantType]] = (acc[TENANT_TYPES[s.tenantType]] || 0) + 1; return acc; }, {});
                labels = Object.keys(tenantCounts);
                data = Object.values(tenantCounts);
                title = 'Servers by Tenant Model';
                type = 'doughnut';
                backgroundColors = theme.palette.chart.backgrounds;
        }
        
        return {
            type,
            data: {
                labels,
                datasets: [{
                    label: 'Count',
                    data,
                    backgroundColor: backgroundColors,
                    borderColor: type === 'line' ? backgroundColors : theme.palette.background.paper,
                    borderWidth: 1,
                    tension: 0.1,
                    fill: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: title },
                    legend: { display: ['pie', 'doughnut'].includes(type) }
                }
            }
        };

    }, [servers, chartType]);

    useEffect(() => {
        if (chartInstance.current) {
            chartInstance.current.destroy();
        }
        if (isVisible && chartRef.current && chartConfig) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, chartConfig);
        }
        return () => {
            if (chartInstance.current) {
                chartInstance.current.destroy();
            }
        };
    }, [chartConfig, isVisible]);

    return (
        <AdminComponents.GraphWrapper>
            {servers.length > 0 ? (
                <AdminComponents.GraphCanvasContainer>
                    <canvas ref={chartRef}></canvas>
                </AdminComponents.GraphCanvasContainer>
            ) : (
                <AdminComponents.CenteredMessage>
                    <Typography>No data to display graphs.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.GraphWrapper>
    );
};


// --- MAIN APP COMPONENT ---
const ServerMaintenance = () => {
    const [servers, setServers] = useState(initialServers);
    const [selectedServer, setSelectedServer] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, server: null, isAdding: false });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Added new server for Synergy Ltd', target: 'Synergy Ltd', timestamp: '7/8/2025, 10:25:00 AM' },
        { user: 'DevOps', action: 'Set Apex Industries server to maintenance', target: 'Apex Industries', timestamp: '7/8/2025, 9:00:00 AM' },
    ]);
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('customer');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [summaryFilter, setSummaryFilter] = useState(null);
    const [chartType, setChartType] = useState('bar');
    
    // Advanced Search State
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    const quickFilterOptions = useMemo(() => {
        const environments = [...new Set(servers.map(s => s.environment))];
        const statuses = [...new Set(servers.map(s => s.status))];
        return [...statuses, ...environments];
    }, [servers]);

    const processedServers = useMemo(() => {
        let current = servers;

        if (summaryFilter) {
            const { type, value } = summaryFilter;
            if (type === 'status') current = current.filter(s => s.status === value);
            if (type === 'serverType') current = current.filter(s => s.serverType === value);
            if (type === 'tenantType') current = current.filter(s => s.tenantType === value);
        }
        
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(c => c.customer.toLowerCase().includes(term));
        }

        if (activeFilters.length > 0) {
            current = current.filter(server => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const serverValue = String(field.includes('.') ? field.split('.').reduce((o, i) => o?.[i], server) : server[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return serverValue === filterValue;
                        case 'Not Equals': return serverValue !== filterValue;
                        case 'Contains': return serverValue.includes(filterValue);
                        case 'Starts With': return serverValue.startsWith(filterValue);
                        case 'Ends With': return serverValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }

        return [...current].sort((a, b) => {
            const valA = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], a) : a[sortColumn];
            const valB = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], b) : b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? (valA || 0) - (valB || 0) : (valB || 0) - (valA || 0);
        });
    }, [servers, searchTerm, activeFilters, sortColumn, sortDirection, summaryFilter]);

    const displayServer = useMemo(() => {
        const isSelectedVisible = processedServers.some(c => c.id === selectedServer?.id);
        if (isSelectedVisible) return selectedServer;
        return processedServers.length > 0 ? processedServers[0] : null;
    }, [processedServers, selectedServer]);

    const addLog = (logEntry) => {
        const timestamp = new Date().toLocaleString();
        setActivityLog(prev => [{ ...logEntry, timestamp }, ...prev].slice(0, 10));
    };

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const confirmDelete = () => {
        setServers(prev => prev.filter(s => !deleteConfirmation.idsToDelete.includes(s.id)));
        addLog({ user: 'Admin', action: `Deleted ${deleteConfirmation.idsToDelete.length} server(s).` });
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
        setSelectedIds([]);
    };
    
    const handleShowDetails = (server) => setModalState({ isOpen: true, server, isAdding: false });
    const handleShowAddModal = () => setModalState({ isOpen: true, server: null, isAdding: true });
    
    const handleSaveServer = (serverData) => {
        if (modalState.isAdding) {
            const newServer = { ...serverData, id: Date.now() };
            setServers(prev => [newServer, ...prev]);
            addLog({ user: 'Admin', action: `Added new server for ${serverData.customer}` });
        } else {
            setServers(prev => prev.map(s => s.id === serverData.id ? serverData : s));
            addLog({ user: 'Admin', action: `Updated server for ${serverData.customer}` });
        }
        setModalState({ isOpen: false, server: null, isAdding: false });
    };

    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedServers.map(c => c.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
    
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) setIsGraphVisible(false);
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };

    const handleGraphToggle = () => {
        setIsGraphVisible(prev => !prev);
        setIsSidebarOpen(false);
    };

    const handleColumnVisibilityChange = (columnKey) => {
        setColumnOrder(prev => prev.includes(columnKey) ? prev.filter(key => key !== columnKey) : [...prev, columnKey]);
    };

    const handleSummaryCardClick = (type, value) => {
        const newFilter = { type, value };
        setSummaryFilter(prev => JSON.stringify(prev) === JSON.stringify(newFilter) ? null : newFilter);
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };
    
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    
    const handleAddQuickFilter = (value) => {
        const field = Object.keys(SERVER_STATUSES).includes(value) ? 'status' : 'environment';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };

    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setSummaryFilter(null);
    };
    
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        setSummaryFilter(null);
    };
    
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev => prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]);
    };
    
    const handleViewModeChange = (e, v) => {
        if (v) {
            setViewMode(v);
            setIsGraphVisible(false);
        }
    };

    const summaryStats = useMemo(() => ({
        total: servers.length,
        online: servers.filter(c => c.status === 'Online').length,
        offline: servers.filter(c => c.status === 'Offline').length,
        maintenance: servers.filter(c => c.status === 'Maintenance').length,
        hcl: servers.filter(c => c.serverType === 'HCL').length,
        singleTenant: servers.filter(c => c.tenantType === 'SINGLE').length,
        multiTenant: servers.filter(c => c.tenantType === 'MULTI').length,
    }), [servers]);

    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isGraphVisible;
        return (
            <AdminComponents.ViewContainer>
                {processedServers.length > 0 ? (
                    <>
                        {viewMode === 'cards' && <AdminComponents.GridView>{processedServers.map(serv => <ServerCard key={serv.id} server={serv} onClick={() => setSelectedServer(serv)} isSelected={displayServer?.id === serv.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(serv.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={isCondensed} />)}</AdminComponents.GridView>}
                        {viewMode === 'compact' && <AdminComponents.CompactView>{processedServers.map(serv => <ServerCompactCard key={serv.id} server={serv} onClick={() => setSelectedServer(serv)} isSelected={displayServer?.id === serv.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(serv.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={isCondensed} />)}</AdminComponents.CompactView>}
                        {viewMode === 'list' && <AdminComponents.ListView>{processedServers.map(serv => <ServerListItem key={serv.id} server={serv} onClick={() => setSelectedServer(serv)} isSelected={displayServer?.id === serv.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(serv.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={isCondensed} />)}</AdminComponents.ListView>}
                        {viewMode === 'grid' && (
                            <ServerTable
                                servers={processedServers}
                                onRowClick={setSelectedServer}
                                onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                                sortColumn={sortColumn}
                                sortDirection={sortDirection}
                                selectedId={displayServer?.id}
                                selectedIds={selectedIds}
                                onSelectAll={handleSelectAll}
                                onSelectOne={handleSelectOne}
                                columnOrder={columnOrder}
                                setColumnOrder={setColumnOrder}
                                addLog={addLog}
                                groupByKeys={groupByKeys}
                                onDelete={handleDeleteRequest}
                                onEdit={handleShowDetails}
                                onView={handleShowDetails}
                                isCondensed={isCondensed}
                            />
                        )}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching Servers</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                <AdminComponents.SummaryCard isActive={!summaryFilter} onClick={() => handleSummaryCardClick(null, null)}>
                                        <AdminComponents.SummaryAvatar variant="total"><Storage /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter?.value === 'Online'} onClick={() => handleSummaryCardClick('status', 'Online')}>
                                        <AdminComponents.SummaryAvatar variant="success"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.online}</Typography><Typography variant="body2">Online</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter?.value === 'Offline'} onClick={() => handleSummaryCardClick('status', 'Offline')}>
                                        <AdminComponents.SummaryAvatar variant="error"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.offline}</Typography><Typography variant="body2">Offline</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter?.value === 'Maintenance'} onClick={() => handleSummaryCardClick('status', 'Maintenance')}>
                                        <AdminComponents.SummaryAvatar variant="warning"><Schedule /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.maintenance}</Typography><Typography variant="body2">Maintenance</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter?.value === 'HCL'} onClick={() => handleSummaryCardClick('serverType', 'HCL')}>
                                        <AdminComponents.SummaryAvatar variant="info"><Dns /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.hcl}</Typography><Typography variant="body2">HCL Servers</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter?.value === 'SINGLE'} onClick={() => handleSummaryCardClick('tenantType', 'SINGLE')}>
                                        <AdminComponents.SummaryAvatar variant="primary"><Cloud /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.singleTenant}</Typography><Typography variant="body2">Single-Tenant</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter?.value === 'MULTI'} onClick={() => handleSummaryCardClick('tenantType', 'MULTI')}>
                                        <AdminComponents.SummaryAvatar variant="secondary"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.multiTenant}</Typography><Typography variant="body2">Multi-Tenant</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={handleShowAddModal}>Add Server</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search by Customer..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedServers.length > 0 && selectedIds.length === processedServers.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedServers.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup size="small" value={viewMode} exclusive onChange={handleViewModeChange}>
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup value={chartType} exclusive onChange={(e, newType) => newType && setChartType(newType)} size="small" fullWidth>
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart">Doughnut<DonutLarge /></ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <ServerGraph servers={processedServers} chartType={chartType} isVisible={isGraphVisible} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>
                
                <Drawer variant="persistent" anchor="right" open={isSidebarOpen}>
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">{sidebarMode === 'search' ? 'Advanced Search' : 'Grid Settings'}</Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}><Close /></IconButton>
                        </AdminComponents.SidebarHeader>
                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.QuickFilterContainer>{quickFilterOptions.map(opt => (<Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />))}</AdminComponents.QuickFilterContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                        <FormControl fullWidth size="small"><InputLabel>Field</InputLabel><Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>{ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}</Select></FormControl>
                                        <FormControl fullWidth size="small"><InputLabel>Operator</InputLabel><Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>{FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}</Select></FormControl>
                                        <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                        <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>{stagedFilters.length > 0 ? stagedFilters.map(f => (<Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />)) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}</AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>{activeFilters.length > 0 ? activeFilters.map(f => (<Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />)) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}</AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnActionContainer><Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button><Button size="small" onClick={() => setColumnOrder([])}>Deselect All</Button></AdminComponents.ColumnActionContainer>
                                        <AdminComponents.ColumnVisibilityContainer>{ALL_COLUMNS.map(col => (<AdminComponents.WrappedFormControlLabel key={col.key} control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} />} label={col.label} />))}</AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>{groupByKeys.length > 0 ? groupByKeys.map(key => (<Chip key={key} label={ALL_COLUMNS.find(c => c.key === key)?.label} onDelete={() => handleGroupByChange(key)} />)) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}</AdminComponents.FilterChipContainer>
                                        <AdminComponents.ColumnVisibilityContainer>{ALL_COLUMNS.filter(c => c.groupable).map(col => (<AdminComponents.WrappedFormControlLabel key={col.key} control={<Checkbox checked={groupByKeys.includes(col.key)} onChange={() => handleGroupByChange(col.key)} />} label={col.label} />))}</AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                        </AdminComponents.SidebarContent>
                        <AdminComponents.SidebarFooter>
                            {sidebarMode === 'search' && (<><Button variant="outlined" onClick={handleResetFilters}>Reset</Button><Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button></>)}
                            {sidebarMode === 'grid' && (<Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>)}
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>
            </AdminComponents.AppContainer>
            
            <ServerDetailsDialog open={modalState.isOpen} onClose={() => setModalState({ isOpen: false, server: null, isAdding: false })} serverData={modalState.server} isAdding={modalState.isAdding} onSave={handleSaveServer} />
            <Dialog open={deleteConfirmation.isOpen} onClose={() => setDeleteConfirmation({ isOpen: false, idsToDelete: [] })}>
                <DialogTitle>Confirm Deletion</DialogTitle>
                <DialogContent><DialogContentText>Are you sure you want to delete {deleteConfirmation.idsToDelete.length} server(s)? This action cannot be undone.</DialogContentText></DialogContent>
                <DialogActions><Button onClick={() => setDeleteConfirmation({ isOpen: false, idsToDelete: [] })}>Cancel</Button><Button onClick={confirmDelete} color="error">Delete</Button></DialogActions>
            </Dialog>
        </ThemeProvider>
    );
};

export default ServerMaintenance;
