export const modulesData = [
    {
        'id': 1,
        'name': 'Sales',
        'category': 'Sales & Service',
        'icon': 'point_of_sale',
        'description': 'Manage sales pipelines, quotes, and orders efficiently.',
        'tags': [
            'Bestseller',
            'Top Rated',
            'Included in Suits'
        ],
        'industries': [
            'Component Manufacturers​',
            'Road Machinery',
            'Agriculture Equipment',
            'CNC Machines',
            'Material Handling',
            'Port Handling',
            'Construction Equipment',
            'Mining Equipment',
            'Aggregate Manufacturers​',
            'Bus',
            'Elevator & Escalators',
            'Trucks',
            'Robots',
            'Marine & Boats',
            'Forestry and Logging',
            'Crushers & Screens',
            'Home Appliances',
            'Industrial Equipments',
            'Power Generation',
            'Industrial',
            'Oil & Gas',
            'Manufacturers​',
            'Healthcare',
            'Defense',
            'Food Processing',
            'Ambulance Health Care Services',
            'Amusement & Recreation',
            'Rail'
        ]
    },
    {
        'id': 2,
        'name': 'CPQ',
        'category': 'Sales & Service',
        'icon': 'request_quote',
        'description': 'Configure, price, and quote complex products with ease.',
        'tags': [
            'Top Rated',
            'OEM Focused'
        ],
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 3,
        'name': 'Dealer Management',
        'category': 'Sales & Service',
        'icon': 'store',
        'description': 'Empower your dealers with tools for sales, service, and parts.',
        'tags': [
            'Bestseller',
            'Mobile Ready'
        ],
        'industries': []
    },
    {
        'id': 4,
        'name': 'Service Van',
        'category': 'Sales & Service',
        'icon': 'local_shipping',
        'description': 'Equip field service technicians with mobile tools and information.',
        'tags': [
            'Mobile Ready',
            'Trending'
        ],
        'industries': []
    },
    {
        'id': 5,
        'name': 'Digital Catalogue',
        'category': 'Sales & Service',
        'icon': 'import_contacts',
        'description': 'Provide interactive and always up-to-date product catalogs.',
        'tags': [
            'Customer Favorite',
            'Included in Suits'
        ],
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 6,
        'name': 'Ecommerce',
        'category': 'Sales & Service',
        'icon': 'shopping_cart',
        'description': 'Launch a powerful B2B/B2C online store integrated with your backend.',
        'tags': [
            'Best Value',
            'Mobile Ready'
        ],
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 7,
        'name': 'Fleet Management',
        'category': 'Sales & Service',
        'icon': 'directions_car',
        'description': 'Monitor and manage vehicle fleets for service and logistics.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 8,
        'name': 'Rental',
        'category': 'Sales & Service',
        'icon': 'event_available',
        'description': 'Manage rental equipment, from contracts to billing and availability.',
        'industries': [
            'Component Manufacturers​',
            'Road Machinery',
            'Agriculture Equipment',
            'CNC Machines',
            'Material Handling',
            'Port Handling',
            'Construction Equipment',
            'Mining Equipment',
            'Bus',
            'Elevator & Escalators',
            'Trucks',
            'Robots',
            'Marine & Boats',
            'Forestry and Logging',
            'Crushers & Screens',
            'Home Appliances',
            'Industrial Equipments',
            'Power Generation',
            'Industrial',
            'Oil & Gas',
            'Manufacturers​',
            'Healthcare',
            'Defense',
            'Food Processing',
            'Ambulance Health Care Services',
            'Amusement & Recreation',
            'Rail'
        ]
    },
    {
        'id': 9,
        'name': 'Reman',
        'category': 'Operations',
        'icon': 'build_circle',
        'description': 'Oversee the entire remanufacturing lifecycle, from core return to final assembly.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 10,
        'name': 'Refurbishment',
        'category': 'Operations',
        'icon': 'recycling',
        'description': 'Streamline the process of refurbishing used parts and assets.',
        'industries': []
    },
    {
        'id': 11,
        'name': 'Inventory',
        'category': 'Operations',
        'icon': 'inventory_2',
        'description': 'Optimize stock levels across multiple locations with advanced forecasting.',
        'industries': []
    },
    {
        'id': 12,
        'name': 'Warehouse Management',
        'category': 'Operations',
        'icon': 'warehouse',
        'description': 'Optimize receiving, put-away, picking, and shipping operations.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 13,
        'name': 'Logistics',
        'category': 'Operations',
        'icon': 'local_shipping',
        'description': 'Manage transportation, shipping, and delivery of parts and products.',
        'industries': [
            'Component Manufacturers​',
            'Road Machinery',
            'Agriculture Equipment',
            'CNC Machines',
            'Material Handling',
            'Port Handling',
            'Construction Equipment',
            'Mining Equipment',
            'Aggregate Manufacturers​',
            'Bus',
            'Elevator & Escalators',
            'Trucks',
            'Robots',
            'Marine & Boats',
            'Forestry and Logging',
            'Crushers & Screens',
            'Home Appliances',
            'Industrial Equipments',
            'Power Generation',
            'Industrial',
            'Oil & Gas',
            'Manufacturers​',
            'Healthcare',
            'Defense',
            'Food Processing',
            'Ambulance Health Care Services',
            'Amusement & Recreation',
            'Rail'
        ]
    },
    {
        'id': 14,
        'name': 'Bay Management',
        'category': 'Operations',
        'icon': 'garage',
        'description': 'Optimize workshop bay scheduling, usage, and technician assignment.',
        'industries': [
            'Road Machinery',
            'Agriculture Equipment',
            'Material Handling',
            'Port Handling',
            'Construction Equipment',
            'Mining Equipment',
            'Bus',
            'Trucks',
            'Robots',
            'Crushers & Screens',
            'Industrial Equipments',
            'Ambulance Health Care Services',
            'Rail'
        ]
    },
    {
        'id': 15,
        'name': 'Site Operations',
        'category': 'Operations',
        'icon': 'domain',
        'description': 'Manage day-to-day operations for physical plant and factory sites.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 16,
        'name': 'Depot Management',
        'category': 'Operations',
        'icon': 'home_work',
        'description': 'Run service and repair depots efficiently, managing workload and resources.',
        'industries': []
    },
    {
        'id': 17,
        'name': 'PDI (Pre-Delivery Inspection)',
        'category': 'Operations',
        'icon': 'fact_check',
        'description': 'Standardize and track pre-delivery inspections for quality assurance.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 18,
        'name': 'Notifications',
        'category': 'Platform & Admin',
        'icon': 'notifications',
        'description': 'Centralized hub for real-time alerts and user communications.',
        'industries': []
    },
    {
        'id': 19,
        'name': 'Multi-Brand',
        'category': 'Platform & Admin',
        'icon': 'dynamic_feed',
        'description': 'Manage multiple brands and business units from a single platform instance.',
        'industries': []
    },
    {
        'id': 20,
        'name': 'Multi-Language',
        'category': 'Platform & Admin',
        'icon': 'translate',
        'description': 'Support global users with a fully translatable interface.',
        'industries': []
    },
    {
        'id': 21,
        'name': 'User Management',
        'category': 'Platform & Admin',
        'icon': 'manage_accounts',
        'description': 'Control user access, roles, and permissions across the platform.',
        'industries': []
    },
    {
        'id': 22,
        'name': 'Role Contract',
        'category': 'Platform & Admin',
        'icon': 'assignment_ind',
        'description': 'Define detailed role-based access contracts for granular security.',
        'industries': []
    },
    {
        'id': 23,
        'name': 'SSO (Single Sign-On)',
        'category': 'Platform & Admin',
        'icon': 'login',
        'description': 'Integrate with your corporate identity provider for seamless login.',
        'industries': []
    },
    {
        'id': 24,
        'name': 'Interface Management',
        'category': 'Platform & Admin',
        'icon': 'hub',
        'description': 'Configure and monitor integrations with third-party systems.',
        'industries': []
    },
    {
        'id': 25,
        'name': 'Command Center',
        'category': 'Platform & Admin',
        'icon': 'monitoring',
        'description': 'Get a high-level overview of all operations from a central dashboard.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 26,
        'name': 'Company',
        'category': 'Company & HR',
        'icon': 'business',
        'description': 'Manage core company information, legal entities, and business units.',
        'industries': []
    },
    {
        'id': 27,
        'name': 'Employee',
        'category': 'Company & HR',
        'icon': 'badge',
        'description': 'Maintain a comprehensive database of all employee information.',
        'industries': []
    },
    {
        'id': 28,
        'name': 'Branch / Location',
        'category': 'Company & HR',
        'icon': 'pin_drop',
        'description': 'Manage all your company locations, branches, and service centers.',
        'industries': []
    },
    {
        'id': 29,
        'name': 'Time & Attendance',
        'category': 'Company & HR',
        'icon': 'schedule',
        'description': 'Automate workforce time tracking, scheduling, and labor data collection.',
        'industries': [
            'Aggregate Manufacturers​'
        ]
    },
    {
        'id': 30,
        'name': 'Helpdesk',
        'category': 'Company & HR',
        'icon': 'support_agent',
        'description': 'Internal ticketing and support system for employees.',
        'industries': [
            'Component Manufacturers​',
            'Road Machinery',
            'Agriculture Equipment',
            'CNC Machines',
            'Material Handling',
            'Port Handling',
            'Construction Equipment',
            'Mining Equipment',
            'Aggregate Manufacturers​',
            'Bus',
            'Elevator & Escalators',
            'Trucks',
            'Robots',
            'Marine & Boats',
            'Forestry and Logging',
            'Crushers & Screens',
            'Home Appliances',
            'Industrial Equipments',
            'Power Generation',
            'Industrial',
            'Oil & Gas',
            'Manufacturers​',
            'Healthcare',
            'Defense',
            'Food Processing',
            'Ambulance Health Care Services',
            'Amusement & Recreation',
            'Rail'
        ]
    },
    {
        'id': 31,
        'name': 'Product/Asset',
        'category': 'Asset & Product',
        'icon': 'widgets',
        'description': 'Central repository for all product and asset master data.',
        'industries': []
    },
    {
        'id': 32,
        'name': 'Parts',
        'category': 'Asset & Product',
        'icon': 'settings',
        'description': 'Manage the entire lifecycle of spare parts, from procurement to sale.',
        'industries': []
    },
    {
        'id': 33,
        'name': 'Tools',
        'category': 'Asset & Product',
        'icon': 'construction',
        'description': 'Track and manage service tools, their calibration, and assignment.',
        'industries': []
    },
    {
        'id': 34,
        'name': 'Special Tools',
        'category': 'Asset & Product',
        'icon': 'build',
        'description': 'Manage high-value, specialized tools with detailed tracking.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 35,
        'name': 'Warranty Management',
        'category': 'Asset & Product',
        'icon': 'verified_user',
        'description': 'Handle warranty claims, entitlements, and supplier recovery.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 36,
        'name': 'Contract Management',
        'category': 'Asset & Product',
        'icon': 'request_page',
        'description': 'Manage customer service contracts, SLAs, and entitlements.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 37,
        'name': 'Checklist Configurator',
        'category': 'Asset & Product',
        'icon': 'checklist',
        'description': 'Create dynamic checklists for inspections, service, and safety.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 38,
        'name': 'MFO (Multi-Functional Object)',
        'category': 'Asset & Product',
        'icon': 'device_hub',
        'description': 'Model complex, configurable products and assets with ease.',
        'industries': []
    },
    {
        'id': 39,
        'name': 'IOT',
        'category': 'Asset & Product',
        'icon': 'sensors',
        'description': 'Connect to and manage data from IoT-enabled assets and vehicles.',
        'industries': []
    },
    {
        'id': 40,
        'name': 'Email',
        'category': 'Communication & Finance',
        'icon': 'email',
        'description': 'Integrated email capabilities for customer and internal communication.',
        'industries': []
    },
    {
        'id': 41,
        'name': 'SMS',
        'category': 'Communication & Finance',
        'icon': 'sms',
        'description': 'Send SMS notifications for service updates, alerts, and marketing.',
        'industries': []
    },
    {
        'id': 42,
        'name': 'Payment',
        'category': 'Communication & Finance',
        'icon': 'payment',
        'description': 'Integrate with payment gateways for seamless transactions.',
        'industries': [
            'Component Manufacturers​'
        ]
    },
    {
        'id': 43,
        'name': 'Float Management',
        'category': 'Communication & Finance',
        'icon': 'attach_money',
        'description': 'Manage cash floats for mobile service vans and depots.',
        'industries': []
    },
    {
        'id': 44,
        'name': 'Dealer Audit',
        'category': 'Communication & Finance',
        'icon': 'rule',
        'description': 'Conduct and track audits of dealer operations and compliance.',
        'industries': [
            'Component Manufacturers​'
        ]
    }
];

export const industriesDataaa = [
    "Component Manufacturers​",
    "Road Machinery​",
    "Agriculture Equipment​",
    "CNC Machines​",
    "Material Handling​",
    "Port Handling​",
    "Construction Equipment​",
    "Mining Equipment​",
    "Aggregate Manufacturing​",
    "Buses​",
    "Elevator & Escalators​",
    "Trucks​",
    "Robots​",
    "Marine & Boats​",
    "Forestry and Logging ",
    "Crushers & Screens",
    "Home Appliances",
    "Industrial Equipments",
    "Power Generation",
    "Industrial",
    "Oil and Gas",
    "Manufacturing",
    "Healthcare",
    "Defense",
    "Food Processing",
    "Ambulatory Health Care Services",
    "Amusement, Gambling, and Recreation Industries",
    "Rail Transportation"
]; 