import React, { useState } from 'react';
import { pricingData } from '../../data/pricing';
import PricingCard from './PricingCard';
// import './Pricing.css';
import { MdCheckCircle, MdCancel, MdExpandMore, MdExpandLess } from 'react-icons/md';
import { FaUserFriends, FaChartBar, FaPuzzlePiece, FaHeadset, FaLock, FaUser, FaPuzzlePiece as FaPuzzle, FaStar, FaCubes, FaGem, FaMedal, FaLeaf } from 'react-icons/fa';

// Feature categories mapping
const featureCategories = [
  {
    name: 'Core Features',
    features: [
      'User Limit',
      'Core Modules Access',
      'Expanded Feature Set',
      'All Modules & Integrations',
    ],
  },
  {
    name: 'Analytics',
    features: [
      'Basic Analytics', 'Standard Analytics', 'Advanced Analytics', 'Custom Analytics & Reporting',
    ],
  },
  {
    name: 'Integrations',
    features: [
      'Basic Integrations', 'Advanced Integrations', 'Custom Integrations', 'API Access',
      'All Modules Integrations',
    ],
  },
  {
    name: 'Support',
    features: [
      'Email Support', 'Priority Email Support', 'Phone & Email Support', 'Dedicated Support Manager',
    ],
  },
  {
    name: 'Access & Security',
    features: [
      'Read-Only Access', 'Read/Write Access', 'Granular Role-Based Access', 'SSO & Advanced Security',
    ],
  },
];

const featureToCategory = {};
featureCategories.forEach(cat => cat.features.forEach(f => { featureToCategory[f] = cat.name; }));

const categoryIcons = {
  'Core Features': FaUserFriends,
  'Analytics': FaChartBar,
  'Integrations': FaPuzzlePiece,
  'Support': FaHeadset,
  'Access & Security': FaLock,
};

const planIcons = [
  <FaLeaf className="plan-icon plan-icon-leaf" />, // Free
  <FaMedal className="plan-icon plan-icon-medal-silver" />, // Silver
  <FaMedal className="plan-icon plan-icon-medal-gold" />, // Gold
  <FaGem className="plan-icon plan-icon-gem" />, // Platinum
];

const Pricing = () => {
    const [billingCycle, setBillingCycle] = useState('monthly'); // 'monthly' or 'annually'
    const [openCategory, setOpenCategory] = useState(featureCategories[0].name);

    const plans = pricingData[billingCycle];

    // Collect all features from all plans
    const allFeatures = Array.from(
        new Set(
            plans.flatMap(plan => plan.features)
        )
    );

    // Group features by category
    const featuresByCategory = featureCategories.map(cat => ({
      ...cat,
      features: cat.features.filter(f => allFeatures.includes(f)),
    })).filter(cat => cat.features.length > 0);

    // Feature enablement logic: if a feature is enabled for a lower plan, it is enabled for all higher plans
    const getFeatureEnabled = (feature, planIdx) => {
      // Platinum always has all features
      if (plans[planIdx].name === 'Platinum') return true;
      // Find the lowest plan index that has this feature
      const firstEnabledIdx = plans.findIndex(p => p.features.includes(feature));
      return firstEnabledIdx !== -1 && planIdx >= firstEnabledIdx;
    };

    // Custom logic for Core Features
    const getCoreFeatureValue = (feature, planIdx) => {
      // User Limit
      if (feature === 'User Limit') {
        return ['1', '5', '20', 'Unlimited'][planIdx];
      }
      // Core Modules Access: all plans
      if (feature === 'Core Modules Access') {
        return <MdCheckCircle style={{ color: 'var(--primary-teal)', fontSize: '1.3rem' }} />;
      }
      // Expanded Feature Set: not for Free, but for others
      if (feature === 'Expanded Feature Set') {
        return planIdx === 0 ? <MdCancel style={{ color: '#e57373', fontSize: '1.3rem' }} /> : <MdCheckCircle style={{ color: 'var(--primary-teal)', fontSize: '1.3rem' }} />;
      }
      // All Modules & Integrations: only for Gold and Platinum
      if (feature === 'All Modules & Integrations') {
        return planIdx < 2 ? <MdCancel style={{ color: '#e57373', fontSize: '1.3rem' }} /> : <MdCheckCircle style={{ color: 'var(--primary-teal)', fontSize: '1.3rem' }} />;
      }
      return null;
    };

    return (
        <section id="pricing" className="pricing-section">
            <div className="container-fluid">
                <div className="section-header">
                    <h2 className="section-title">Flexible Plans for Teams of All Sizes</h2>
                    <p className="section-subtitle">
                        Choose a plan that fits your needs. Switch or cancel anytime.
                    </p>
                </div>

                <div className="billing-toggle">
                    <span className={`toggle-label ${billingCycle === 'monthly' ? 'active' : ''}`}>
                        Monthly
                    </span>
                    <label className="switch">
                        <input
                            type="checkbox"
                            onChange={() => setBillingCycle(prev => prev === 'monthly' ? 'annually' : 'monthly')}
                            checked={billingCycle === 'annually'}
                        />
                        <span className="slider round"></span>
                    </label>
                    <span className={`toggle-label ${billingCycle === 'annually' ? 'active' : ''}`}>
                        Annually
                        <span className="save-badge">Save 15%</span>
                    </span>
                </div>

                <div className="pricing-row">
                    {plans.map((plan, index) => {
                      // Quick view logic for core features
                      const quickView = [
                        {
                          icon: index === 0
                            ? <MdCancel className="plan-quickview-icon plan-quickview-icon-cancel" />
                            : <MdCheckCircle className="plan-quickview-icon plan-quickview-icon-check" />,
                          text: [
                            'Up to 1 User',
                            'Up to 5 Users',
                            'Up to 20 Users',
                            'Unlimited Users',
                          ][index],
                        },
                        {
                          icon: <MdCheckCircle className="plan-quickview-icon plan-quickview-icon-check" />,
                          text: 'Core Modules Access',
                        },
                        {
                          icon: index === 0
                            ? <MdCancel className="plan-quickview-icon plan-quickview-icon-cancel" />
                            : <MdCheckCircle className="plan-quickview-icon plan-quickview-icon-check" />,
                          text: 'Expanded Feature Set',
                        },
                        {
                          icon: index < 2
                            ? <MdCancel className="plan-quickview-icon plan-quickview-icon-cancel" />
                            : <MdCheckCircle className="plan-quickview-icon plan-quickview-icon-check" />,
                          text: 'All Modules & Integrations',
                        },
                      ];
                      return (
                        <div key={index} className={`pricing-summary-card${plan.isPopular ? ' popular' : ''}`}>
                          <div className="plan-header-row">
                            <div className="plan-name-with-icon">{planIcons[index]}{plan.name}</div>
                            {plan.isPopular && <div className="most-popular-badge">Most Popular</div>}
                          </div>
                          <div className="plan-summary">{plan.summary}</div>
                          <div className="plan-price">{plan.price}<span className="price-period">{plan.period}</span></div>
                          <hr className="plan-separator" />
                          <ul className="plan-quickview">
                            {quickView.map((item, i) => (
                              <li key={i} className="plan-quickview-item">
                                {item.icon}
                                <span className="plan-quickview-value">{item.text}</span>
                              </li>
                            ))}
                          </ul>
                          <button className="button-pricing button-primary">{plan.buttonText}</button>
                        </div>
                      );
                    })}
                </div>

                <div className="feature-comparison-section full-width">
                    <h3 className="feature-comparison-title">Detailed Feature Comparison</h3>
                    <div className="feature-comparison-accordion">
                      {featuresByCategory.map(cat => (
                        <div className="feature-category" key={cat.name}>
                          <button
                            className={`feature-category-header${openCategory === cat.name ? ' open' : ''}`}
                            onClick={() => setOpenCategory(openCategory === cat.name ? null : cat.name)}
                          >
                            <span>
                              {React.createElement(categoryIcons[cat.name], {
                                className: `feature-category-icon${openCategory === cat.name ? ' open' : ''}`
                              })}
                              {cat.name}
                            </span>
                            {openCategory === cat.name ? <MdExpandLess /> : <MdExpandMore />}
                          </button>
                          <div className={`feature-category-body${openCategory === cat.name ? ' open' : ''}`}
                          >
                            <table className="feature-comparison-table">
                              <thead>
                                <tr>
                                  <th>Feature</th>
                                  {plans.map((plan, idx) => (
                                    <th key={idx}>{plan.name}</th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody>
                                {cat.features.map((feature, idx) => (
                                  <tr key={idx}>
                                    <td>{feature}</td>
                                    {cat.name === 'Core Features'
                                      ? plans.map((plan, pidx) => (
                                          <td key={pidx} className={feature === 'User Limit' ? 'user-limit-cell' : ''}>{getCoreFeatureValue(feature, pidx)}</td>
                                        ))
                                      : plans.map((plan, pidx) => (
                                          <td key={pidx} className="feature-comparison-center">{getFeatureEnabled(feature, pidx) ? <MdCheckCircle className="feature-comparison-icon feature-comparison-icon-check" /> : <MdCancel className="feature-comparison-icon feature-comparison-icon-cancel" />}</td>
                                        ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Pricing; 