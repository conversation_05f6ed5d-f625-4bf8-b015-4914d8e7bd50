import React from 'react';
import { useLocation, Link as RouterLink } from 'react-router-dom';
import { Typography, Breadcrumbs as MuiBreadcrumbs } from '@mui/material';
import { FiChevronRight, FiHome } from 'react-icons/fi';
import { AdminComponents } from '../../../styles/theme';

const breadcrumbNameMap = {
  '/admin': 'Dashboard',
  '/admin/OnBoarding': 'On Boarding',
  '/admin/contract': 'Contract Management',
  '/admin/purchase': 'Purchase Orders',
  '/admin/invoice': 'Invoice',
  '/admin/reciepts': 'Reciepts',
  '/admin/professional': 'Professional Services',
  '/admin/license': 'Licensce Management',
  '/admin/user': 'User',
  '/admin/sla': 'SLA',
  '/admin/branches': 'Branches',
  '/admin/travel': 'Travel Details',
  '/admin/support': 'Product Support',
};

const Breadcrumbs = () => {
  const location = useLocation();
  
  // --- HIDE BREADCRUMBS ON MAIN DASHBOARD ---
  if (location.pathname === '/admin') {
    return null; 
  }

  const pathnames = location.pathname.split('/').filter((x) => x);

  return (
    // --- FIX: Set padding to 0 to remove all vertical spacing ---
    <AdminComponents.BreadcrumbsContainer>
        <MuiBreadcrumbs separator={<FiChevronRight size="14" />} aria-label="breadcrumb">
            <AdminComponents.BreadcrumbsLink component={RouterLink} underline="hover" color="inherit" to="/admin">
                <FiHome size="16" />
                Dashboard
            </AdminComponents.BreadcrumbsLink>
            {pathnames.slice(1).map((value, index) => {
            const last = index === pathnames.length - 2;
            const to = `/admin/${pathnames.slice(1, index + 2).join('/')}`;
            const name = breadcrumbNameMap[to] || value.charAt(0).toUpperCase() + value.slice(1);

            return last ? (
                <Typography color="text.primary" key={to} sx={{ display: 'flex', alignItems: 'center' }}>
                {name}
                </Typography>
            ) : (
                <AdminComponents.BreadcrumbsLink component={RouterLink} underline="hover" color="inherit" to={to} key={to}>
                {name}
                </AdminComponents.BreadcrumbsLink>
            );
            })}
      </MuiBreadcrumbs>
    </AdminComponents.BreadcrumbsContainer>
  );
};

export default Breadcrumbs;