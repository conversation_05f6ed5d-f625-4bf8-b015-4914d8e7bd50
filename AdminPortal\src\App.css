/* fonts.css */
/* HCLTechRoobert-Light */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

/* HCLTechRoobert-Regular */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

/* HCLTechRoobert-Medium */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
}

/* HCLTechRoobert-Bold */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* HCLTechRoobert-ExtraBold */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-ExtraBold.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
}

/* HCLTechRoobert-Heavy */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Heavy.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
}

:root {
    /* -------------------------------------------------------------------------- */
    /* Primary Colors                              */
    /* -------------------------------------------------------------------------- */
    --primary-teal: #2EC0CB;
    --primary-teal-dark: #23A3AD;
    --primary-blue: #0F5FDC;
    --primary-white: #FFFFFF;
    --light-teal: #A4F4FF;
    --primary-color: #2EC0CB;
    --primary-color-dark-hover: #00d6c6;
    --primary-color-hover: #009e9e;
    /* -------------------------------------------------------------------------- */
    /* Secondary Colors                              */
    /* -------------------------------------------------------------------------- */
    --secondary-teal-dark: #23A3AD;
    --secondary-teal-light: #2EC0CB;
    --secondary-teal-extra-light: #FFFFFF;
    --secondary-blue-light: #0F5FDC;
    --secondary-blue-extra-light: #8CC8FA;
    --secondary-gray-light: #DCE6F0;
    /* -------------------------------------------------------------------------- */
    /* Tertiary Colors                               */
    /* -------------------------------------------------------------------------- */
    --tertiary-navy: #000032;
    --tertiary-gray-light: #ECF3F8;
    --tertiary-black: #000000;
    --card-bg: #f7f7fc;
    /* -------------------------------------------------------------------------- */
    /* Neutral & Text Colors                           */
    /* -------------------------------------------------------------------------- */
    --text-primary: #000032;
    --text-secondary: #3a4a6b;
    --text-tertiary: #222222;
    --text-link: #0F5FDC;
    --text-link-hover: #0F5FDC;
    --text-white: #FFFFFF;
    --text-accent: #23A3AD;
    --text-accent-hover: #2EC0CB;
    --text-label: #6a7ba3;
    --text-error: #e53e3e;
    --text-error-alt: #e53935;
    --text-error-light: #e57373;
    --text-link-active: #0056d6;
    --text-sidebar-cart: #005e6a;
    --text-logo-sub-default: #1a237e;
    --text-feature-category: #181818;
    --logo-main-color-default: #23A3AD;
    --text-color-dark-1: #2d3748;
    --text-color-dark-2: #4a5568;
    --text-color-light-1: #718096;
    --text-color-light-2: #a0aec0;
    --text-disabled: #bdbdbd;
    --topbar-icon: #222b45;
    --filter-text-color: #006080;
    /* -------------------------------------------------------------------------- */
    /* Background Colors                              */
    /* -------------------------------------------------------------------------- */
    --background-primary: #ECF3F8;
    --background-secondary: #FFFFFF;
    --background-overlay: #00000099;
    --background-accent-light: #e0f7fa;
    --background-hover-light: #e0f4fa;
    --background-overlay-dark: #00000080;
    --background-overlay-light: #0000002E;
    --background-sidebar-overlay: #0020402E;
    --background-accent-light-alt: #e6fafd;
    --background-accent-dark-alt: #f7fafc;
    --background-overlay-cart-drag: #007BFF1A;
    --background-cart-hover: #edf2f7;
    --background-subscription-selected: #ebf8ff;
    --background-contact-phone: #2EC0CB1A;
    --background-contact-phone-hover: #2EC0CB26;
    --avatar-bg: #eef2ff;
    --avatar-color: #4338ca;
    --background-toggle-view: #f2f7fd;
    --background-toggle-view-hover: #e0eafc;
    --background-scrollbar-track: #f1f1f1;
    --background-scrollbar-thumb: #c1c1c1;
    --background-scrollbar-thumb-hover: #a8a8a8;
    --background-industry-header: #F5FAFFFA;
    --background-feature-category: #f3f8fc;
    --background-filter-pill: #f2f8fa;
    --partner-logo-bg-color: #1a9ba8;
    /* -------------------------------------------------------------------------- */
    /* Border Colors                                */
    /* -------------------------------------------------------------------------- */
    --border-color: #DCE6F0;
    --border-color-accent: #2EC0CB;
    --border-color-dark: #23A3AD;
    --border-color-button: #b2e0ea;
    --border-color-filter: #f0f2f5;
    --border-color-light: #e0e6ed;
    --border-color-light-1: #e2e8f0;
    --border-color-light-2: #cbd5e0;
    --border-color-blue: #4299e1;
    --border-color-blue-light: #007bff;
    /* -------------------------------------------------------------------------- */
    /* Gradient Colors                              */
    /* -------------------------------------------------------------------------- */
    --hcl-gradient-color: linear-gradient(269.2deg, #3C91FF 1.02%, #2EC0CB 99.5%);
    --background-gradient-about: linear-gradient(45deg, #FFFFFF 60%, #e6fafd 100%);
    --background-gradient-about-overlay: linear-gradient(120deg, #2EC0CB 0%, #23A3AD 50%, #000032 100%);
    --background-gradient-cta: linear-gradient(90deg, #2EC0CB, #0F5FDC);
    --background-gradient-cta-hover: linear-gradient(90deg, #0F5FDC, #2EC0CB);
    --background-overlay-about-shine: linear-gradient(120deg, #2EC0CB1F 0%, #0F5FDC2E 100%);
    --border-image-gradient-cta: linear-gradient(90deg, #2EC0CB, #0F5FDC) 1;
    --border-image-gradient-cta-hover: linear-gradient(90deg, #0F5FDC, #2EC0CB) 1;
    --gradient-submit: linear-gradient(135deg, #2EC0CB 0%, #009e9e 100%);
    --gradient-submit-hover: linear-gradient(135deg, #009e9e 0%, #2EC0CB 100%);
    --gradient-submit-shine: linear-gradient(90deg, transparent, #FFFFFF33, transparent);
    --gradient-hero: linear-gradient(45deg, #2EC0CB, #23A3AD, #000032);
    --gradient-marquee-right: linear-gradient(to right, #FFFFFF, transparent);
    --gradient-marquee-left: linear-gradient(to left, #FFFFFF, transparent);
    --gradient-suite-tag: linear-gradient(90deg, #2EC0CB, #0F5FDC);
    --delete-gradient: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);
    --checkout-gradient: linear-gradient(90deg, #2EC0CB 60%, #23A3AD 100%);
    --checkout-gradient-hover: linear-gradient(90deg, #23A3AD 0%, #2EC0CB 100%);
    /* -------------------------------------------------------------------------- */
    /* Tag & Status Colors                            */
    /* -------------------------------------------------------------------------- */
    --tag-color-gold: #FFD700;
    --tag-color-orange: #ff9800;
    --tag-color-yellow: #ffd600;
    --tag-color-cyan: #00b6e6;
    --tag-color-purple: #5e35b1;
    --tag-color-green: #8bc34a;
    --tag-color-violet: #7c3aed;
    --tag-color-ocean: #0080c6;
    --tag-color-teal: #00897b;
    --tag-color-amber: #ffb300;
    /* -------------------------------------------------------------------------- */
    /* Theme Status Color Palette                        */
    /* -------------------------------------------------------------------------- */
    --success-main: #4CAF50;
    --success-dark: #689f38;
    --success-light: #dcedc8;
    --warning-main: #FFD600;
    --warning-dark: #f57c00;
    --warning-light: #fff3e0;
    --error-main: #e53e3e;
    --error-dark: #b71c1c;
    --error-light: #ffcdd2;
    --error-hover-color: #b71c1c;
    --info-main: #0F5FDC;
    --info-dark: #1565c0;
    --info-light: #bbdefb;
    /* -------------------------------------------------------------------------- */
    /* Summary Card Icon Colors                          */
    /* -------------------------------------------------------------------------- */
    --total-main: #51bd17;
    --active-main: #23A3AD;
    --inactive-main: #DCE6F0;
    --expired-main: #BDBDBD;
    --paid-main: #4CAF50;
    --unpaid-main: #e53e3e;
    --partially-paid-main: #FFD600;
    --approved-main: #23A3AD;
    --pending-main: #FFD600;
    --secondary-main: #2EC0CB;
    --primary-main: #0F5FDC;
    /* -------------------------------------------------------------------------- */
    /* Chart Colors                               */
    /* -------------------------------------------------------------------------- */
    --chart-bg-1: #2EC0BBB3;
    --chart-bg-2: #17707FB3;
    --chart-bg-3: #4338CAB3;
    --chart-bg-4: #F59E0BB3;
    /* -------------------------------------------------------------------------- */
    /* Component & Miscellaneous Colors                   */
    /* -------------------------------------------------------------------------- */
    --scrollbar-thumb-color-hover: #006666;
    --sidebar-close-btn-color: #0080c6;
    --accent-color-checkbox: #00b6e6;
    --plan-icon-color-silver: #b0b0b0;
    --platform-toggle-active: #14b8a6;
    --platform-toggle-active-bg: #f0fdfa;
    --platform-toggle-active-bg-alt: #ccfbf1;
    --platform-toggle-color: #444444;
    --platform-border-color: #d1d5db;
    --pagination-hover-bg: #f3f4f6;
    --pagination-hover-border: #9ca3af;
    --search-clear-bg: #e5e7eb;
    --search-clear-text: #6b7280;
    --search-clear-text-hover: #374151;
    --success-icon-color: #48bb78;
    --checkout-hover-bg: #0056b3;
    /* -------------------------------------------------------------------------- */
    /* Shadows                                  */
    /* -------------------------------------------------------------------------- */
    --shadow-xs: 0 1px 2px #2C3E500D;
    --shadow-sm: 0 2px 8px #2C3E5014;
    --shadow-md: 0 4px 16px #2EC0CB26;
    --shadow-lg: 0 8px 32px #2C3E5021;
    --shadow-xl: 0 8px 40px #2C3E502E;
    --shadow-inset: 2px 0 12px 0 #0F5FDC0A;
    --shadow-sm-alt: 0 2px 8px #2C3E500F;
    --shadow-md-alt: 0 4px 16px #2C3E501A;
    --shadow-hero: 0 2px 4px #00000033;
    --shadow-btn: 0 2px 10px #2EC0CB21;
    --shadow-sidebar: 0 2px 8px 0 #0040800A;
    --shadow-sidebar-mobile: 0 0 32px 0 #0000002E;
    --shadow-sidebar-mobile-alt: 0 2px 8px 0 #0000000F;
    --shadow-sidebar-open: 0 0 32px 0 #2C3E5014;
    --shadow-filter-sidebar: 0 4px 24px #2EC0CB14;
    --shadow-cart-card: 0 4px 6px -1px #0000001A, 0 2px 4px -1px #0000000F;
    --shadow-cart-card-hover: 0 10px 15px -3px #2C3E501A, 0 4px 6px -2px #2C3E500D;
    --shadow-delete-hover: 0 4px 12px #E53E3E4D;
    --shadow-checkout-hover: 0 4px 12px #007BFF4D;
    --shadow-add-hover: 0 4px 12px #00000026;
    --shadow-platform-toggle: 0 1px 2px #00000008;
    --shadow-platform-toggle-active: 0 2px 8px #14B8A614;
    --shadow-contact-form: 0 20px 60px #00000014;
    --shadow-contact-phone-hover: 0 8px 25px #2EC0CB33;
    --shadow-contact-input-focus: 0 0 0 4px #2EC0CB1A;
    --shadow-contact-input-invalid: 0 0 0 4px #E53E3E1A;
    --shadow-module-card: 0 4px 15px #0000000D;
    --shadow-module-card-hover: 0 8px 30px #0000001A;
    --shadow-solutions-tab: 0 2px 12px 0 #0080C614;
    --shadow-solutions-filter: 0 6px 32px #2EC0CB1A;
    --shadow-card-selected: 0 0 0 2px #2EC0CB;
    --shadow-none: none;
    /* -------------------------------------------------------------------------- */
    /* Sizing - Font                               */
    /* -------------------------------------------------------------------------- */
    --font-family-base: 'HCLTech Roobert', sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-heavy: 900;
    --font-size-Small: 0.80rem;
    --font-size-base: 1rem;
    --font-size-xxl: 1.5rem;
    --font-size-xxxl: 2rem;
    --font-size-hero: 3.5rem;
    --font-size-2_5-rem: 2.5rem;
    --font-size-4-rem: 4rem;
    --font-size-relative-lg: 1.1em;
    --font-size-relative-xl: 1.2em;
    --font-size-relative-xxl: 1.3em;
    --line-height-base: 1.5;
    --line-height-lg: 1.7;
    --line-height-sm: 1;
    --font-size-2_6-rem: 2.6rem;
    --font-size-2_7-rem: 2.7rem;
    --font-size-1_12-rem: 1.12rem;
    --font-size-1_15-rem: 1.15rem;
    --font-size-1_18-rem: 1.18rem;
    --font-size-0_75-rem: 0.75rem;
    --font-size-0_8-rem: 0.8rem;
    --font-size-0_9-rem: 0.9rem;
    --font-size-0_78-rem: 0.78rem;
    --font-size-0_92-rem: 0.92rem;
    --font-size-1_22-rem: 1.22rem;
    --font-size-1_4-rem: 1.4rem;
    --font-size-1_01-rem: 1.01rem;
    --font-size-0_93-rem: 0.93rem;
    --font-size-icon: 1.1rem;
    /* -------------------------------------------------------------------------- */
    /* Sizing - Spacing & Units                          */
    /* -------------------------------------------------------------------------- */
    --space-none: 0;
    --space-xxs: 4px;
    --space-xs: 8px;
    --space-sm: 12px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-xld: 40px;
    --space-xxl: 64px;
    --space-90px: 90px;
    --space-unit: 1rem;
    --space-unit-sm: 0.5rem;
    --space-unit-md: 1.5rem;
    --space-unit-lg: 2rem;
    --space-unit-xl: 2.5rem;
    --space-unit-xxl: 3rem;
    --space-unit-xxxl: 4rem;
    --space-0_2-rem: 0.2rem;
    --space-0_3-rem: 0.3rem;
    --space-0_4-rem: 0.4rem;
    --space-0_6-rem: 0.6rem;
    --space-0_7-rem: 0.7rem;
    --space-1_1-rem: 1.1rem;
    --space-1_2-rem: 1.2rem;
    --space-1_6-rem: 1.6rem;
    --space-0_45-rem: 0.45rem;
    --space-sidebar-padding-y: 18px;
    --space-sidebar-padding-x: 14px;
    --space-sidebar-gap: 14px;
    --space-sidebar-header-margin: 6px;
    --space-filter-group-padding: 10px;
    --space-filter-pills-gap: 6px;
    --space-filter-pill-padding-y: 6px;
    --space-filter-pill-padding-x: 14px;
    --space-filter-pill-margin-y: 1px;
    --space-filter-pill-margin-x: 2px;
    --space-filter-link-padding-y: 4px;
    --space-filter-link-padding-x: 10px;
    --space-filter-link-margin-bottom: 2px;
    --space-industry-checkbox-gap: 10px;
    --space-solution-type-gap: 7px;
    --space-sidebar-footer-gap: 18px;
    --space-icon-margin-right: 4px;
    --space-search-padding-y: 7px;
    --space-search-padding-x: 10px;
    --space-industry-list-gap: 4px;
    --space-0_32-rem: 0.32rem;
    /* -------------------------------------------------------------------------- */
    /* Sizing - Borders & Radii                          */
    /* -------------------------------------------------------------------------- */
    --border-width-sm: 1px;
    --border-width-md: 1.5px;
    --border-width-lg: 2px;
    --border-width-xl: 3px;
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 22px;
    --radius-xxl: 50px;
    --radius-full: 50%;
    --radius-sidebar: 14px;
    --radius-button-sm: 24px;
    --radius-filter-sidebar: 18px;
    --radius-card: 16px;
    /* -------------------------------------------------------------------------- */
    /* Transitions & Animations                           */
    /* -------------------------------------------------------------------------- */
    --transition-duration-fast: 0.18s;
    --transition-duration-base: 0.22s;
    --transition-duration-slow: 0.32s;
    --transition-duration-filter-sidebar: 0.35s;
    --transition-opacity-filter-sidebar: 0.25s;
    --transition-easing-ease: ease;
    --transition-easing-cubic: cubic-bezier(0.4, 0, 0.2, 1);
    --transition-easing-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animation-duration: 0.7s;
    --animation-duration-faq: 1s;
    --transition-duration-color: 0.2s;
    --fab-delay: 0ms;
    --animation-duration-long: 15s;
    --animation-duration-medium: 1.2s;
    --animation-duration-short: 0.9s;
    --animation-duration-micro: 0.5s;
    --animation-delay-sm: 0.1s;
    --animation-delay-md: 0.2s;
    --animation-delay-lg: 0.4s;
    --animation-duration-hero: 15s;
    --animation-duration-marquee: 60s;
    --animation-transition-slider: 0.4s;
    /* -------------------------------------------------------------------------- */
    /* Z-Index                                   */
    /* -------------------------------------------------------------------------- */
    --z-index-default: 1;
    --z-index-low: 10;
    --z-index-low-mid: 10;
    --z-index-mid: 20;
    --z-index-mid-high: 30;
    --z-index-high: 2000;
    --z-index-modal: 1000;
    --z-index-header: 2000;
    --z-index-sidebar: 2102;
    --z-index-fab: 1200;
    --z-index-sidebar-overlay: 1001;
    --z-index-sidebar-advanced: 1002;
    --z-index-appbar: 100;
    /* -------------------------------------------------------------------------- */
    /* Layout                                   */
    /* -------------------------------------------------------------------------- */
    --sidebar-width: 260px;
    --sidebar-width-collapsed: 70px;
    --sidebar-min-width: 220px;
    --sidebar-max-width: 270px;
    --sidebar-top: 80px;
    --sidebar-mobile-min-width: 80vw;
    --sidebar-mobile-max-width: 90vw;
    --sidebar-mobile-min-width-alt: 70vw;
    --auth-modal-width: 960px;
    --auth-modal-height: 600px;
    --auth-modal-min-width: 320px;
    --industry-image-thumb-size: 40px;
    --industry-image-thumb-size-sm: 32px;
    --industry-checkbox-min-height: 48px;
    --industry-icon-size: 18px;
    --industry-card-height: 140px;
    --industry-list-max-height: 245px;
    --min-height-sidebar: 400px;
    --breakpoint-md: 900px;
    --breakpoint-lg: 1400px;
    --max-width-ui: 1280px;
    --max-width-ui-lg: 1440px;
    --fab-icon-size: 36px;
    --filter-group-col-min-width: 180px;
    /* -------------------------------------------------------------------------- */
    /* Miscellaneous (Non-Color)                       */
    /* -------------------------------------------------------------------------- */
    --transform-btn-hover: translateY(-2px);
    --transform-sidebar-closed: translateX(-110%);
    --transform-sidebar-closed-alt: translateX(-120%);
    --transform-sidebar-open: translateX(0);
    --transform-fab-action: translateY(20px);
    --letter-spacing-normal: 0;
    --letter-spacing-logo-main: 0.01em;
    --letter-spacing-logo-sub: 0.08em;
    --letter-spacing-filter-label: 0.2px;
    --password-strength-width: 0%;
    --opacity-medium: 0.5;
    --logo-sub-margin-top: -0.2em;
}





/* === Layout === */
.app-container {
    display: flex;
    height: 100vh;
}

.main-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.main-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-height: 0;
    flex: 1;
}

.content {
    flex: 1 1 0;
    overflow-y: auto;
    min-height: 0;
    padding: var(--space-unit-lg);
    background: var(--primary-white);
}


/* AuthModal.css */
.auth-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-modal);
    min-height: 100vh;
    min-width: 100vw;
}

.auth-modal-card {
    background: transparent;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: var(--auth-modal-width);
    height: var(--auth-modal-height);
    min-width: var(--auth-modal-min-width);
    min-height: 400px;
    max-width: 98vw;
    max-height: 98vh;
    padding: var(--space-none);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    overflow: hidden;
    animation: modalFadeIn var(--transition-duration-slow) var(--transition-easing-cubic);
}

@media (max-width: 1100px) {
    .auth-modal-card {
        width: 98vw;
        height: 98vh;
        min-width: var(--space-none);
        min-height: var(--space-none);
    }
}

.auth-modal-2col {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
}

.auth-modal-2col-inner {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
}

.auth-modal-2col-left {
    width: 50%;
    min-width: var(--auth-modal-min-width);
    height: 100%;
    background: var(--secondary-teal-extra-light);
    border-radius: var(--radius-xl) var(--space-none) var(--space-none) var(--radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2.6rem 1.2rem 2.6rem 2.2rem;
    box-shadow: var(--shadow-inset);
    background: var(--light-teal);
}

.auth-modal-2col-right {
    width: 50%;
    min-width: var(--auth-modal-min-width);
    height: 100%;
    background: var(--primary-white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2.6rem 2.2rem 2.6rem 1.2rem;
    position: relative;
}

.auth-modal-content {
    width: 80%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.auth-modal-content-inner {
    width: 100%;
    max-width: 420px;
    min-height: 420px;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 1 auto;
    margin: var(--space-none) auto;
    position: relative;
}

.auth-modal-tabs {
    display: flex;
    justify-content: center;
    gap: 3rem;
}

.auth-modal-tab-content-wrapper {
    position: relative;
    min-height: 420px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.signup-form, .login-form {
    margin-bottom: var(--space-unit-md);
}

.social-login-options {
    margin-top: 1.2rem;
    margin-bottom: var(--space-unit-sm);
    width: 100%;
}

.social-login-row {
    display: flex;
    flex-direction: row;
    gap: var(--space-none);
    width: 100%;
}

.social-login-btn {
    flex: 1 1 0;
    width: 100%;
    max-width: none;
}

.auth-modal-switch-link {
    text-align: center;
    margin-top: 1.1rem;
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

    .auth-modal-switch-link span {
        color: var(--text-link);
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        text-decoration: underline;
        margin-left: 0.2em;
        transition: color var(--transition-duration-fast);
    }

        .auth-modal-switch-link span:hover,
        .auth-modal-switch-link span:focus {
            color: var(--text-link-hover);
            text-decoration: underline;
        }

@media (max-width: 900px) {
    .auth-modal-card {
        width: 100vw;
        height: 100vh;
        border-radius: var(--space-none);
    }

    .auth-modal-2col,
    .auth-modal-2col-inner {
        flex-direction: column;
    }

    .auth-modal-2col-left, .auth-modal-2col-right {
        width: 100%;
        min-width: var(--space-none);
        border-radius: var(--space-none);
        height: auto;
        min-height: 220px;
        padding: 1.2rem var(--space-unit);
    }
}

.auth-modal-close {
    position: absolute;
    top: var(--space-lg);
    right: var(--space-xl);
    background: none;
    border: none;
    padding: var(--space-none);
    cursor: pointer;
    z-index: var(--z-index-mid);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-modal-close-bg {
    background: var(--primary-white);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-sm);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: box-shadow var(--transition-duration-fast);
}

.auth-modal-close:hover .auth-modal-close-bg,
.auth-modal-close:focus .auth-modal-close-bg {
    box-shadow: 0 4px 16px rgba(0,150,167,0.18);
}

.auth-modal-close svg {
    display: block;
    width: 28px;
    height: 28px;
    stroke: var(--primary-teal-dark);
}

.auth-modal-branding {
    text-align: center;
    width: 100%;
}

    .auth-modal-branding .logo {
        margin-bottom: 1.2rem;
        width: 100%;
        height: auto;
    }

.auth-modal-headline {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--tertiary-navy);
    margin-bottom: 1.1rem;
    margin-top: 0.2rem;
    letter-spacing: 0.01em;
}

.auth-modal-values {
    list-style: none;
    padding: var(--space-none);
    margin: var(--space-none);
    color: var(--text-secondary);
    font-size: 1.04rem;
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-base);
    text-align: left;
    margin-top: 1.1rem;
}

    .auth-modal-values li {
        margin-bottom: 0.4rem;
        display: flex;
        align-items: flex-start;
        gap: 0.7em;
    }

        .auth-modal-values li:before {
            content: '\2714';
            color: var(--primary-teal);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-bold);
            margin-right: 0.4em;
            line-height: 1.2;
        }

.auth-modal-tab {
    background: none;
    border: none;
    outline: none;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-tertiary);
    padding: var(--space-unit-sm) 0.75rem 0.25rem 0.75rem;
    cursor: pointer;
    position: relative;
    transition: color var(--transition-duration-fast);
    border-radius: var(--space-none);
}

    .auth-modal-tab.active {
        font-weight: var(--font-weight-bold);
        color: var(--primary-teal);
    }

.auth-modal-tab-underline {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -2px;
    height: var(--border-width-xl);
    border-radius: var(--border-width-lg);
    background: var(--primary-teal);
    z-index: var(--z-index-low);
}

.auth-modal-tab-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--space-unit-sm) var(--space-none);
    margin-top: 0.75rem;
}

.social-login-btn.icon-only {
    margin: var(--space-none);
    padding: var(--space-none);
    background: none;
    border: none;
    box-shadow: none;
    width: 48px;
    height: 48px;
    min-width: 48px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-login-icon-circle {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    transition: none;
}

.social-login-btn.icon-only:hover .social-login-icon-circle,
.social-login-btn.icon-only:focus .social-login-icon-circle {
    box-shadow: 0 2px 8px rgba(0,182,182,0.18);
}

.social-login-img {
    width: 26px;
    height: 26px;
    object-fit: contain;
}

.forgot-title {
    text-align: center;
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--tertiary-navy);
}

.forgot-desc {
    text-align: center;
}

.auth-link-text {
    color: var(--primary-teal-dark) !important;
    font-weight: var(--font-weight-semibold);
    position: relative;
    cursor: pointer;
    transition: color var(--transition-duration-base) var(--transition-easing-cubic);
}

    .auth-link-text::after {
        content: '';
        position: absolute;
        left: var(--space-none);
        bottom: -2px;
        width: var(--space-none);
        height: var(--border-width-lg);
        background: var(--primary-teal-dark);
        border-radius: var(--border-width-sm);
        transition: width var(--transition-duration-slow) var(--transition-easing-cubic);
    }

    .auth-link-text:hover,
    .auth-link-text:focus {
        color: var(--primary-teal);
    }

        .auth-link-text:hover::after,
        .auth-link-text:focus::after {
            width: 100%;
        }

.header-profile-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: var(--space-unit-md);
}

.header-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: var(--radius-xl);
    padding: var(--space-0_2-rem) var(--space-0_9-rem, 0.9rem) var(--space-0_2-rem) var(--space-0_2-rem);
    transition: background var(--transition-duration-fast);
}

    .header-profile:hover, .header-profile:focus {
        background: var(--background-accent-light);
    }

.header-profile-photo {
    width: var(--space-1_6-rem, 1.6rem);
    height: var(--space-1_6-rem, 1.6rem);
    border-radius: var(--radius-full);
    object-fit: cover;
    margin-right: var(--space-0_7-rem);
    border: var(--border-width-lg) solid var(--border-color-accent);
    background: var(--primary-white);
}

.header-profile-name {
    font-weight: var(--font-weight-semibold);
    color: var(--text-accent);
    font-size: var(--font-size-base);
    letter-spacing: 0.01em;
}

.header-profile-menu {
    position: absolute;
    top: 110%;
    right: var(--space-none);
    background: var(--primary-white);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 24px rgba(var(--shadow-color-rgb), 0.10);
    min-width: 170px;
    z-index: var(--z-index-high);
    padding: var(--space-unit-sm) var(--space-none);
    display: flex;
    flex-direction: column;
    animation: fadeInProfileMenu var(--transition-duration-base) var(--transition-easing-cubic);
}

.header-profile-menu-item {
    padding: var(--space-0_7-rem) var(--space-1_2-rem);
    color: var(--text-accent);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    border: none;
    background: none;
    text-align: left;
}

    .header-profile-menu-item:hover, .header-profile-menu-item:focus {
        background: var(--background-accent-light);
        color: var(--text-accent-hover);
    }

@keyframes fadeInProfileMenu {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* FabMenu.css */
.fab-menu {
    position: fixed;
    z-index: var(--z-index-fab);
    right: var(--space-unit-xl);
    bottom: var(--space-unit-xl);
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-end;
    pointer-events: none;
}

.fab-main {
    width: var(--space-xxl);
    height: var(--space-xxl);
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--primary-teal), var(--primary-blue));
    color: var(--primary-white);
    border: none;
    box-shadow: var(--shadow-lg);
    font-size: var(--font-size-xxxl);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background var(--transition-duration-slow), transform var(--transition-duration-base);
    pointer-events: auto;
}

    .fab-main:hover {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-teal));
        transform: scale(1.08);
    }

.fab-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-bottom: var(--space-unit);
}

.fab-action {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    pointer-events: none;
    margin-bottom: var(--space-unit);
    width: 52px;
    height: 52px;
    border-radius: var(--radius-full);
    background: var(--primary-white);
    color: var(--primary-teal);
    border: none;
    box-shadow: var(--shadow-md);
    font-size: var(--font-size-xxl);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: opacity var(--transition-duration-slow), transform var(--transition-duration-slow), background var(--transition-duration-base), color var(--transition-duration-base);
}

    .fab-action:hover {
        background: var(--primary-teal);
        color: var(--primary-white);
    }

.fab-menu.open .fab-action {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

.fab-tooltip {
    position: absolute;
    right: 110%;
    top: 50%;
    transform: translateY(-50%) scale(0.95);
    background: var(--primary-teal);
    color: var(--primary-white);
    padding: 0.4rem var(--space-unit);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-base);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-duration-base), transform var(--transition-duration-base);
    box-shadow: 0 2px 8px rgba(var(--shadow-color-accent-rgb), 0.12);
}

.fab-action:hover .fab-tooltip {
    opacity: 1;
    transform: translateY(-50%) scale(1);
}

.assistant-fab-main {
    position: fixed;
    left: var(--space-unit-xl);
    bottom: var(--space-unit-xl);
    z-index: var(--z-index-fab);
    width: var(--space-xxl);
    height: var(--space-xxl);
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--primary-teal), var(--primary-blue));
    color: var(--primary-white);
    border: none;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background var(--transition-duration-slow), transform var(--transition-duration-base);
    pointer-events: auto;
}

    .assistant-fab-main:hover {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-teal));
        transform: scale(1.08);
    }

@media (max-width: 600px) {
    .fab-menu {
        right: var(--space-unit);
        bottom: var(--space-unit);
    }

    .fab-main {
        width: 52px;
        height: 52px;
        font-size: var(--font-size-xxl);
    }

    .fab-action {
        width: 44px;
        height: 44px;
        font-size: var(--font-size-base);
    }

    .assistant-fab-main {
        left: var(--space-unit);
        bottom: var(--space-unit);
        width: 52px;
        height: 52px;
    }

        .assistant-fab-main img {
            width: 28px;
            height: 28px;
        }
}

/* SignUp.css */
.signup-form-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    justify-content: center;
}

.signup-form {
    width: 100%;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    margin: var(--space-none) auto;
}

.signup-fields {
    display: flex;
    flex-direction: column;
    gap: 1.1rem;
}

.signup-field-row {
    width: 100%;
}

.floating-label-group {
    position: relative;
    width: 100%;
    margin-bottom: 0.2rem;
}

.floating-input {
    width: 100%;
    padding: 1.1rem var(--space-unit) var(--space-unit-sm) var(--space-unit);
    font-size: var(--font-size-base);
    border: var(--border-width-lg) solid var(--border-color);
    border-radius: 14px;
    background: var(--background-secondary);
    outline: none;
    transition: border-color var(--transition-duration-base);
    box-shadow: none;
    position: relative;
    z-index: var(--z-index-default);
}

    .floating-input:focus {
        border-color: var(--primary-color);
    }

.floating-label-group label {
    position: absolute;
    left: 1.1rem;
    top: 1.1rem;
    color: var(--text-label);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    pointer-events: none;
    background: var(--background-secondary);
    padding: var(--space-none) 0.3em var(--space-none) 0.2em;
    border-radius: var(--radius-sm);
    transition: all var(--transition-duration-fast) var(--transition-easing-cubic);
    z-index: var(--z-index-low);
    line-height: var(--line-height-sm);
    display: flex;
    align-items: center;
}

    .floating-label-group label .required {
        color: var(--primary-color);
        font-weight: var(--font-weight-bold);
        margin-left: 0.15em;
    }

    .floating-input:focus + label,
    .floating-label-group label.filled {
        top: -0.85rem;
        left: 0.9rem;
        font-size: 1.01rem;
        color: var(--primary-color);
        font-weight: var(--font-weight-bold);
        background: var(--background-secondary);
        padding: var(--space-none) var(--space-unit-sm);
        z-index: var(--border-width-xl);
    }

.floating-label-group select + label {
    top: -0.85rem;
    left: 0.9rem;
    font-size: 1.01rem;
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
    background: var(--background-secondary);
    padding: var(--space-none) var(--space-unit-sm);
    z-index: var(--border-width-xl);
}

.input-border-anim {
    display: none;
}

.input-error-text {
    color: var(--text-error);
    font-size: 0.92rem;
    margin-top: 0.18rem;
    margin-left: 0.2rem;
    min-height: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.01em;
    transition: opacity var(--transition-duration-base);
}

.signup-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--space-unit-sm);
    margin-bottom: var(--space-none);
}

.password-strength-meter {
    width: 100%;
    height: 7px;
    background: var(--border-color);
    border-radius: var(--space-xxs);
    margin-top: 0.2rem;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    background: linear-gradient(90deg, #ffb347 0%, #4caf50 100%);
    border-radius: var(--space-xxs);
    transition: width var(--transition-duration-slow) var(--transition-easing-cubic);
}

.signup-spinner {
    display: inline-block;
    width: 1.2em;
    height: 1.2em;
    border: 2.5px solid var(--primary-white);
    border-top: 2.5px solid var(--text-link);
    border-radius: var(--radius-full);
    animation: spin var(--animation-duration) linear infinite;
    vertical-align: middle;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.signup-step-indicator {
    text-align: center;
    font-size: 1.02rem;
    color: var(--text-label);
    font-weight: var(--font-weight-medium);
    margin-bottom: 0.7rem;
    letter-spacing: 0.01em;
}

.signup-row {
    display: flex;
    gap: 1.1rem;
    width: 100%;
    margin-bottom: 0.1rem;
}

.signup-field-col {
    flex: 1 1 0;
    min-width: var(--space-none);
    display: flex;
    flex-direction: column;
}

.signup-field-col-full {
    flex: 1 1 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.signup-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.1rem;
    width: 100%;
}

.signup-grid-full {
    grid-column: 1 / 3;
}

@media (max-width: 600px) {
    .signup-grid {
        grid-template-columns: 1fr;
        gap: 0.7rem var(--space-none);
    }

    .signup-grid-full {
        grid-column: 1 / 2;
    }
}

.password-group {
    position: relative;
}

.show-hide-toggle {
    position: absolute;
    right: 1.1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: var(--font-size-base);
    color: var(--primary-color);
    cursor: pointer;
    user-select: none;
    font-weight: var(--font-weight-semibold);
    z-index: 4;
    background: transparent;
    border: none;
    padding: var(--space-none) 0.2em;
    border-radius: var(--border-width-xl);
    transition: color var(--transition-duration-fast);
    outline: none;
    display: flex;
    align-items: center;
}

    .show-hide-toggle:hover, .show-hide-toggle:focus {
        color: var(--primary-color-hover);
        background: var(--background-primary);
    }

    .show-hide-toggle svg {
        display: block;
    }

.social-login-options {
    margin-top: var(--space-unit-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.7rem;
}

.social-login-divider {
    width: 100%;
    text-align: center;
    margin: 0.7rem var(--space-none) var(--space-unit-sm) var(--space-none);
    color: #b0b8c9;
    font-size: var(--font-size-base);
    position: relative;
}

    .social-login-divider span {
        background: var(--background-secondary);
        padding: var(--space-none) 0.7em;
        position: relative;
        z-index: var(--z-index-low);
    }

    .social-login-divider:before,
    .social-login-divider:after {
        content: '';
        display: block;
        position: absolute;
        top: 50%;
        width: 40%;
        height: var(--border-width-sm);
        background: var(--border-color);
    }

    .social-login-divider:before {
        left: var(--space-none);
    }

    .social-login-divider:after {
        right: var(--space-none);
    }


.forgot-password-link {
    width: 100%;
    text-align: right;
    margin: 0.2rem var(--space-none) 0.7rem var(--space-none);
}

    .forgot-password-link span {
        color: var(--text-link);
        font-size: 0.97rem;
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        text-decoration: underline;
        transition: color var(--transition-duration-fast);
    }

        .forgot-password-link span:hover,
        .forgot-password-link span:focus {
            color: var(--text-link-hover);
            text-decoration: underline;
        }

.login-form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: var(--space-unit-sm) var(--space-none);
    width: 100%;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.45em;
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
}

    .remember-me input[type="checkbox"] {
        display: none;
    }

.custom-checkbox {
    width: var(--font-size-base);
    height: var(--font-size-base);
    border: var(--border-width-lg) solid var(--primary-color);
    border-radius: var(--space-xxs);
    display: inline-block;
    position: relative;
    margin-right: 0.2em;
    background: var(--background-secondary);
    transition: border-color var(--transition-duration-fast);
}

.remember-me input[type="checkbox"]:checked + .custom-checkbox {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

    .remember-me input[type="checkbox"]:checked + .custom-checkbox:after {
        content: '';
        position: absolute;
        left: 0.28em;
        top: 0.05em;
        width: 0.35em;
        height: 0.65em;
        border: solid var(--primary-white);
        border-width: var(--space-none) 0.18em 0.18em var(--space-none);
        transform: rotate(45deg);
        display: block;
    }

.login-btn {
    width: 100%;
    background: var(--primary-color);
    color: var(--text-white);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-base);
    border-radius: var(--space-lg);
    padding: 0.7em var(--space-none);
    margin: var(--space-unit-sm) var(--space-none);
    border: none;
    box-shadow: var(--shadow-sm);
    transition: background var(--transition-duration-fast), box-shadow var(--transition-duration-fast), color var(--transition-duration-fast);
}

    .login-btn:hover, .login-btn:focus {
        background: var(--primary-color-dark-hover);
        color: var(--text-white);
        box-shadow: 0 4px 16px rgba(0,182,182,0.16);
    }

/* Footer.css */
.site-footer {
    background-color: var(--tertiary-navy);
    color: var(--tertiary-gray-light);
    padding: var(--space-unit-xxxl) var(--space-none) var(--space-unit-lg);
    font-size: var(--font-size-base);
}

.footer-main {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--space-unit-xxl);
    margin-bottom: var(--space-unit-xxl);
}

.footer-column.about p {
    margin-top: var(--space-unit);
    color: var(--secondary-gray-light);
    line-height: var(--line-height-base);
}

.social-links {
    margin-top: var(--space-unit-md);
    display: flex;
    gap: var(--space-unit);
}

    .social-links a {
        color: var(--tertiary-gray-light);
        font-size: var(--font-size-xxl);
        transition: color var(--transition-duration-slow) var(--transition-easing-ease), transform var(--transition-duration-slow) var(--transition-easing-ease);
    }

        .social-links a:hover {
            color: var(--primary-teal);
            transform: translateY(-2px);
        }

.footer-heading {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-unit-md);
    color: var(--primary-white);
}

.footer-column.links ul {
    list-style: none;
    padding: var(--space-none);
    margin: var(--space-none);
}

.footer-column.links li {
    margin-bottom: 0.75rem;
}

.footer-column.links a, .footer-column.contact a {
    color: var(--tertiary-gray-light);
    text-decoration: none;
    transition: color var(--transition-duration-slow) var(--transition-easing-ease);
}

    .footer-column.links a:hover, .footer-column.contact a:hover {
        color: var(--primary-teal);
    }

.footer-column.contact p {
    line-height: var(--line-height-base);
    color: var(--tertiary-gray-light);
}

.footer-bottom {
    border-top: var(--border-width-sm) solid var(--border-color-dark);
    padding-top: var(--space-unit-lg);
    text-align: center;
    font-size: 0.9rem;
    color: var(--secondary-gray-light);
}

/* Header.css */
.site-header {
    background-color: var(--primary-white);
    padding: var(--space-unit-sm) var(--space-none);
    position: fixed;
    top: var(--space-none);
    left: var(--space-none);
    width: 100vw;
    z-index: var(--z-index-header);
    box-shadow: var(--shadow-sm-alt);
    transition: all var(--transition-duration-slow) var(--transition-easing-in-out);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: var(--space-none) auto;
}

.header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-nav ul {
    display: flex;
    gap: var(--space-unit-lg);
    list-style: none;
}

.main-nav a {
    text-decoration: none;
    color: var(--tertiary-navy);
    font-weight: var(--font-weight-medium);
    position: relative;
    transition: color var(--transition-duration-slow) var(--transition-easing-ease);
}

    .main-nav a::after {
        content: '';
        position: absolute;
        width: var(--space-none);
        height: var(--border-width-lg);
        bottom: calc(var(--space-xs) * -1); /* -5px replaced with -8px (closest) */
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--primary-teal);
        transition: width var(--transition-duration-slow) var(--transition-easing-ease);
    }

    .main-nav a:hover,
    .main-nav a.active {
        color: var(--primary-teal);
    }

        .main-nav a:hover::after,
        .main-nav a.active::after {
            width: 100%;
        }

.button-primary {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    padding: 0.75rem var(--space-unit-md);
    border-radius: var(--radius-xxl);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-slow) var(--transition-easing-ease), transform var(--transition-duration-base) var(--transition-easing-ease);
    box-shadow: var(--shadow-md-alt);
}

    .button-primary:hover {
        background-color: var(--primary-teal-dark);
        transform: translateY(-2px);
    }

.has-submenu {
    position: relative;
}

.nav-link {
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    color: var(--tertiary-navy);
    padding: var(--space-unit-sm) var(--space-1_2-rem);
    border-radius: var(--radius-xxl);
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
}

.has-submenu:hover .submenu,
.has-submenu:focus-within .submenu {
    display: block;
    opacity: 1;
    pointer-events: auto;
}

.submenu {
    display: none;
    position: absolute;
    top: 110%;
    left: var(--space-none);
    min-width: 160px;
    background: var(--primary-white);
    box-shadow: 0 8px 32px rgba(var(--shadow-color-accent-rgb),0.13);
    border-radius: var(--radius-lg);
    padding: var(--space-unit-sm) var(--space-0_2-rem);
    z-index: var(--z-index-high);
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-duration-base);
}

    .submenu li {
        list-style: none;
    }

.submenu-link {
    background: none;
    border: none;
    color: var(--tertiary-navy);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    padding: var(--space-0_7-rem) var(--space-unit-md);
    width: 100%;
    text-align: left;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    outline: none;
}

    .submenu-link:hover, .submenu-link:focus {
        background: var(--primary-teal);
        color: var(--primary-white);
    }

#hero {
    margin-top: 75px;
}

.auth-actions {
    display: flex;
    gap: var(--space-unit);
    align-items: center;
    margin-left: var(--space-unit-md);
}

.btn.auth-signup {
    background: var(--text-link);
    color: var(--text-white);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--space-lg);
    padding: var(--space-unit-sm) var(--space-unit-md);
    box-shadow: 0 2px 8px rgba(var(--shadow-color-primary-rgb),0.08);
    transition: transform var(--transition-duration-fast) var(--transition-easing-cubic), background var(--transition-duration-fast), box-shadow var(--transition-duration-fast);
    border: none;
    outline: none;
}

    .btn.auth-signup:hover, .btn.auth-signup:focus {
        transform: translateY(-3px) scale(1.04);
        background: var(--text-link-hover);
        box-shadow: 0 4px 16px rgba(var(--shadow-color-primary-rgb),0.16);
    }

.btn.auth-login {
    background: transparent;
    color: var(--text-link);
    font-weight: var(--font-weight-semibold);
    border: var(--border-width-lg) solid var(--text-link);
    border-radius: var(--space-lg);
    padding: var(--space-unit-sm) var(--space-unit-md);
    position: relative;
    overflow: hidden;
    transition: color var(--transition-duration-fast), border-color var(--transition-duration-fast);
    outline: none;
}

    .btn.auth-login::after {
        content: '';
        display: block;
        position: absolute;
        left: var(--space-none);
        bottom: var(--space-none);
        width: 0%;
        height: var(--border-width-lg);
        background: var(--text-link);
        transition: width var(--transition-duration-slow) var(--transition-easing-cubic);
    }

    .btn.auth-login:hover, .btn.auth-login:focus {
        color: var(--text-link-hover);
        border-color: var(--text-link-hover);
    }

        .btn.auth-login:hover::after, .btn.auth-login:focus::after {
            width: 100%;
        }

@media (max-width: 1200px) {
    .main-nav {
        display: none;
    }

    .header-hamburger {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        background: none;
        border: none;
        cursor: pointer;
        z-index: 2101;
        margin-right: var(--space-unit);
    }

        .header-hamburger span {
            display: block;
            width: 26px;
            height: var(--border-width-xl);
            background: var(--primary-teal);
            margin: 3px var(--space-none);
            border-radius: var(--border-width-lg);
            transition: all var(--transition-duration-base);
        }

    .header-sidebar {
        position: fixed;
        top: var(--space-none);
        left: var(--space-none);
        width: 270px;
        height: 100vh;
        background: var(--background-secondary);
        box-shadow: 2px 0 24px rgba(var(--shadow-color-rgb),0.10);
        z-index: var(--z-index-sidebar);
        transform: translateX(-100%);
        transition: transform 0.28s var(--transition-easing-cubic);
        display: flex;
        flex-direction: column;
        padding: 1.2rem 0.7rem 1.2rem 1.2rem;
    }

        .header-sidebar.open {
            transform: translateX(0);
        }

    .header-sidebar-close {
        background: none;
        border: none;
        font-size: 2.2rem;
        color: var(--primary-teal);
        align-self: flex-end;
        margin-bottom: 1.2rem;
        cursor: pointer;
        z-index: 2103;
    }

    .header-sidebar-nav {
        list-style: none;
        padding: var(--space-none);
        margin: var(--space-none);
        display: flex;
        flex-direction: column;
        gap: var(--space-unit-sm);
    }

        .header-sidebar-nav li {
            margin-bottom: 0.2rem;
        }

        .header-sidebar-nav .nav-link,
        .header-sidebar-nav a,
        .header-sidebar-nav .submenu-link,
        .header-sidebar-nav .auth-link-text,
        .header-sidebar-nav .header-profile-menu-item {
            font-size: var(--font-size-base);
            color: var(--secondary-teal-dark);
            font-weight: var(--font-weight-medium);
            padding: 0.7rem 0.8rem;
            border-radius: var(--radius-md);
            background: none;
            border: none;
            text-align: left;
            width: 100%;
            display: block;
            transition: background var(--transition-duration-fast), color var(--transition-duration-fast);
            cursor: pointer;
        }

            .header-sidebar-nav a.btn.btn-primary {
                margin-top: 1.2rem;
                width: 90%;
                text-align: center;
                align-self: center;
            }

            .header-sidebar-nav .nav-link:hover,
            .header-sidebar-nav a:hover,
            .header-sidebar-nav .submenu-link:hover,
            .header-sidebar-nav .auth-link-text:hover,
            .header-sidebar-nav .header-profile-menu-item:hover {
                background: var(--background-accent-light);
                color: var(--text-accent-hover);
            }

    .header-sidebar-backdrop {
        position: fixed;
        top: var(--space-none);
        left: var(--space-none);
        right: var(--space-none);
        bottom: var(--space-none);
        background: var(--background-overlay-light);
        z-index: 2100;
        animation: fadeInSidebarBackdrop var(--transition-duration-base) var(--transition-easing-cubic);
    }

    @keyframes fadeInSidebarBackdrop {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}

@media (min-width: 1201px) {
    .header-hamburger,
    .header-sidebar,
    .header-sidebar-backdrop {
        display: none;
    }
}

.header-cart-login-row {
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.header-cart-icon-wrap {
    position: relative;
    display: flex;
    align-items: center;
}

.header-cart-icon {
    font-size: var(--font-size-xxxl);
    color: var(--primary-teal);
    cursor: pointer;
    transition: color var(--transition-duration-fast);
}

.header-cart-badge {
    position: absolute;
    top: calc(var(--space-xs) * -1.75); /* -7px ≈ -8px */
    right: calc(var(--space-xs) * -1.25); /* -10px ≈ -8px */
    background: var(--text-error);
    color: var(--text-white);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    border-radius: var(--radius-full);
    padding: var(--space-xxs) var(--space-0_7-rem);
    min-width: var(--font-size-icon);
    text-align: center;
    box-shadow: 0 2px 8px rgba(var(--shadow-color-error-rgb),0.18);
    pointer-events: none;
    z-index: var(--z-index-low);
}

.header-cart-login-row .auth-link-text {
    margin-left: var(--space-1_2-rem) !important;
    margin-right: var(--space-1_2-rem) !important;
}

.header-cart-login-row .header-profile-wrapper {
    margin-left: var(--space-1_2-rem) !important;
    margin-right: var(--space-0_2-rem) !important;
}

/* Cart Header Styles */
.cart-header-custom {
    background-color: var(--primary-white);
    padding: var(--space-unit-sm) var(--space-none);
    position: fixed;
    top: var(--space-none);
    left: var(--space-none);
    width: 100vw;
    z-index: var(--z-index-header);
    box-shadow: var(--shadow-sm-alt);
}

    .cart-header-custom .header-inner {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: var(--space-unit-lg);
    }

.cart-header-nav {
    display: flex;
    gap: var(--space-unit-lg);
    list-style: none;
    cursor: pointer;
    margin-left: 135px;
}

.cart-header-link {
    text-decoration: none;
    color: var(--tertiary-navy);
    font-weight: var(--font-weight-medium);
    position: relative;
    transition: color var(--transition-duration-slow) var(--transition-easing-ease);
}

    .cart-header-link::after {
        content: '';
        position: absolute;
        width: var(--space-none);
        height: var(--border-width-lg);
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--primary-teal);
        transition: width var(--transition-duration-slow) var(--transition-easing-ease);
    }

    .cart-header-link:hover {
        color: var(--primary-teal);
    }

        .cart-header-link:hover::after {
            width: 100%;
        }

    .cart-header-link:has(span:contains("Cart")) {
        display: flex;
        align-items: center;
        gap: var(--space-unit-sm);
    }

.cart-header-custom .header-cart-login-row {
    display: flex;
    align-items: center;
    gap: var(--space-unit-md);
}

.cart-header-custom .btn.btn-primary {
    background: var(--primary-teal);
    color: var(--primary-white);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-xxl);
    padding: var(--space-0_7-rem) var(--space-1_6-rem);
    box-shadow: 0 2px 10px rgba(var(--shadow-color-accent-rgb),0.13);
    transition: background var(--transition-duration-base), color var(--transition-duration-base), box-shadow var(--transition-duration-base), transform var(--transition-duration-base);
    border: none;
    outline: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

    .cart-header-custom .btn.btn-primary:hover {
        background: var(--primary-teal-dark);
        color: var(--primary-white);
        transform: translateY(-2px);
    }

.cart-header-custom .header-profile-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: var(--space-unit-md);
}

.cart-header-custom .header-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: var(--radius-xl);
    padding: var(--space-0_2-rem) var(--space-0_9-rem, 0.9rem) var(--space-0_2-rem) var(--space-0_2-rem);
    transition: background var(--transition-duration-fast);
}

    .cart-header-custom .header-profile:hover,
    .cart-header-custom .header-profile:focus {
        background: var(--background-accent-light);
    }

.cart-header-custom .header-profile-photo {
    width: var(--space-1_6-rem, 1.6rem);
    height: var(--space-1_6-rem, 1.6rem);
    border-radius: var(--radius-full);
    object-fit: cover;
    margin-right: var(--space-0_7-rem);
    border: var(--border-width-lg) solid var(--primary-teal);
    background: var(--background-secondary);
}

.cart-header-custom .header-profile-name {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-teal-dark);
    font-size: var(--font-size-base);
    letter-spacing: 0.01em;
}

.cart-header-custom .header-profile-menu {
    position: absolute;
    top: 110%;
    right: var(--space-none);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 24px rgba(var(--shadow-color-rgb),0.10);
    min-width: 170px;
    z-index: var(--z-index-high);
    padding: var(--space-unit-sm) var(--space-none);
    display: flex;
    flex-direction: column;
    animation: fadeInProfileMenu var(--transition-duration-base) var(--transition-easing-cubic);
}

.cart-header-custom .header-profile-menu-item {
    padding: var(--space-0_7-rem) var(--space-1_2-rem);
    color: var(--secondary-teal-dark);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    border: none;
    background: none;
    text-align: left;
}

    .cart-header-custom .header-profile-menu-item:hover,
    .cart-header-custom .header-profile-menu-item:focus {
        background: var(--background-accent-light);
        color: var(--text-accent-hover);
    }

@keyframes fadeInProfileMenu {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cart-contact-modal-backdrop {
    position: fixed;
    top: var(--space-none);
    left: var(--space-none);
    width: 100vw;
    height: 100vh;
    background-color: var(--background-overlay-dark);
    z-index: 3000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cart-contact-modal {
    background: var(--primary-white);
    border-radius: var(--radius-lg);
    padding: var(--space-unit-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.cart-contact-modal-close {
    position: absolute;
    top: var(--space-unit);
    right: var(--space-unit);
    background: none;
    border: none;
    font-size: var(--font-size-xxl);
    cursor: pointer;
    color: var(--tertiary-navy);
    z-index: var(--z-index-default);
}

    .cart-contact-modal-close:hover {
        color: var(--primary-teal);
    }

@media (max-width: 768px) {
    .cart-header-nav {
        gap: var(--space-unit-md);
        flex-wrap: wrap;
    }

    .cart-header-link {
        font-size: var(--font-size-base);
        padding: 0.4rem var(--space-none);
    }

    .cart-header-custom .header-inner {
        flex-wrap: wrap;
        gap: var(--space-unit);
    }

    .cart-header-custom .cart-header-nav {
        display: none;
    }

    .cart-header-custom .header-hamburger {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: var(--space-lg);
        height: 20px;
        background: none;
        border: none;
        cursor: pointer;
        padding: var(--space-none);
        margin-left: auto;
    }

        .cart-header-custom .header-hamburger span {
            width: 100%;
            height: var(--border-width-lg);
            background: var(--primary-teal);
            transition: all var(--transition-duration-slow) var(--transition-easing-ease);
        }
}

@media (min-width: 769px) {
    .cart-header-custom .header-hamburger,
    .cart-header-custom .header-sidebar,
    .cart-header-custom .header-sidebar-backdrop {
        display: none;
    }
}

.cart-header-custom .header-sidebar {
    position: fixed;
    top: var(--space-none);
    right: -300px;
    width: 300px;
    height: 100vh;
    background: var(--primary-white);
    box-shadow: -4px 0 24px rgba(var(--shadow-color-rgb),0.15);
    z-index: 2200;
    transition: right var(--transition-duration-slow) var(--transition-easing-cubic);
    overflow-y: auto;
}

    .cart-header-custom .header-sidebar.open {
        right: var(--space-none);
    }

.cart-header-custom .header-sidebar-close {
    position: absolute;
    top: var(--space-unit);
    right: var(--space-unit);
    background: none;
    border: none;
    font-size: var(--font-size-xxl);
    cursor: pointer;
    color: var(--tertiary-navy);
    z-index: var(--z-index-default);
}

.cart-header-custom .header-sidebar-nav {
    padding: var(--space-unit-xxxl) var(--space-unit-lg) var(--space-unit-lg) var(--space-unit-lg);
    list-style: none;
    margin: var(--space-none);
}

    .cart-header-custom .header-sidebar-nav li {
        margin-bottom: var(--space-unit);
    }

    .cart-header-custom .header-sidebar-nav .nav-link,
    .cart-header-custom .header-sidebar-nav a {
        display: block;
        padding: 0.7rem 0.8rem;
        border-radius: var(--radius-md);
        background: none;
        border: none;
        text-align: left;
        width: 100%;
        transition: background var(--transition-duration-fast), color var(--transition-duration-fast);
        cursor: pointer;
        text-decoration: none;
        color: var(--tertiary-navy);
        font-weight: var(--font-weight-medium);
    }

        .cart-header-custom .header-sidebar-nav a.btn.btn-primary {
            margin-top: 1.2rem;
            width: 90%;
            text-align: center;
            align-self: center;
        }

        .cart-header-custom .header-sidebar-nav .nav-link:hover,
        .cart-header-custom .header-sidebar-nav a:hover,
        .cart-header-custom .header-sidebar-nav .header-profile-menu-item:hover {
            background: var(--background-accent-light);
            color: var(--text-accent-hover);
        }

.cart-header-custom .header-sidebar-backdrop {
    position: fixed;
    top: var(--space-none);
    left: var(--space-none);
    right: var(--space-none);
    bottom: var(--space-none);
    background: var(--background-overlay-light);
    z-index: 2100;
    animation: fadeInSidebarBackdrop var(--transition-duration-base) var(--transition-easing-cubic);
}

/* Logo.css */
.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--tertiary-navy);
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
}

.logo-text-main {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    letter-spacing: -1px;
}

.logo-text-sub {
    font-size: 0.875rem;
    font-weight: var(--font-weight-normal);
    color: var(--tertiary-navy);
    text-transform: uppercase;
    letter-spacing: var(--border-width-md);
}

/* AboutUs.css */
.about-section {
    width: 100vw;
    padding: var(--space-unit-xxxl) var(--space-none);
    background: var(--background-gradient-about);
    position: relative;
    overflow: hidden;
}

    .about-section::before {
        content: '';
        position: absolute;
        top: var(--space-none);
        left: var(--space-none);
        width: 100%;
        height: 100%;
        z-index: var(--z-index-default);
        background: var(--background-gradient-about-overlay);
        opacity: 0.10;
        background-size: 200% 200%;
        animation: aboutBgMove var(--animation-duration-long) var(--transition-easing-in-out) infinite;
        pointer-events: none;
    }

@keyframes aboutBgMove {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.container {
    width: 100%;
    max-width: none;
    margin: var(--space-none);
    padding: var(--space-none);
}

.about-content {
    position: relative;
    z-index: var(--z-index-low);
    width: 100%;
    max-width: none;
    margin: var(--space-none);
    background: rgba(255, 255, 255, 0.98);
    border-radius: var(--space-lg);
    box-shadow: 0 var(--space-xs) var(--space-xl) rgba(var(--shadow-color-accent-rgb), 0.10), 0 var(--border-width-lg) var(--space-xs) rgba(var(--shadow-color-rgb), 0.04);
    padding: var(--space-unit-xxl) 3vw var(--space-unit-xl) 3vw;
    display: flex;
    flex-direction: column;
    gap: 2.2rem;
    animation: fadeInAbout var(--animation-duration-medium) var(--transition-easing-cubic) both;
}

@keyframes fadeInAbout {
    from {
        opacity: 0;
        transform: translateY(40px);
    }

    to {
        opacity: 1;
        transform: none;
    }
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-unit-sm);
}

.section-title {
    font-size: var(--font-size-2_7-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    margin-bottom: var(--space-unit-sm);
    letter-spacing: -0.5px;
}

.section-subtitle {
    color: var(--primary-blue);
}

.about-mission {
    font-size: var(--font-size-1_18-rem);
    font-weight: var(--font-weight-medium);
    color: var(--tertiary-navy);
    text-align: center;
    margin-bottom: var(--space-unit-sm);
}

.about-industry-challenge, .about-solution, .about-heritage-vision {
    background: var(--background-accent-dark-alt);
    border-radius: var(--space-md);
    padding: var(--space-unit-md) 1.2rem 1.2rem 1.2rem;
    box-shadow: 0 var(--border-width-lg) var(--space-sm) rgba(var(--shadow-color-rgb), 0.04);
    margin-bottom: var(--space-unit-sm);
    animation: fadeInAbout var(--animation-duration-medium) var(--transition-easing-cubic) both;
}

    .about-industry-challenge h3, .about-solution h3, .about-heritage-vision h3 {
        color: var(--primary-teal);
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-bold);
        margin-bottom: var(--space-unit-sm);
    }

.about-cta {
    text-align: center;
    margin-top: 1.2rem;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.2rem;
    margin-top: 1.2rem;
}

.cta-btn {
    display: inline-block;
    padding: var(--font-size-base) var(--space-unit-xl);
    border-radius: var(--radius-xxl);
    font-size: var(--font-size-1_12-rem);
    font-weight: var(--font-weight-bold);
    color: var(--primary-teal);
    background: var(--background-accent-light-alt);
    border: 2.5px solid var(--primary-teal);
    text-decoration: none;
    transition: all 0.25s var(--transition-easing-cubic);
    box-shadow: 0 var(--border-width-lg) var(--space-xs) rgba(var(--shadow-color-accent-rgb), 0.07);
    position: relative;
    overflow: hidden;
}

    .cta-btn:after {
        content: '';
        position: absolute;
        left: -60%;
        top: var(--space-none);
        width: 60%;
        height: 100%;
        background: var(--background-overlay-about-shine);
        transform: skewX(-20deg);
        transition: left var(--animation-duration-micro);
        z-index: var(--z-index-default);
        pointer-events: none;
    }

    .cta-btn:hover:after {
        left: 110%;
        transition: left var(--animation-duration-micro);
    }

    .cta-btn:hover {
        background: var(--background-accent-light-alt);
        color: var(--primary-blue);
        border-width: var(--border-width-xl);
        box-shadow: 0 var(--space-xs) var(--space-xl) rgba(var(--shadow-color-accent-rgb), 0.13);
    }

.cta-btn-primary {
    background: var(--background-gradient-cta);
    color: var(--text-white);
    border: 2.5px solid var(--primary-blue);
    font-weight: var(--font-weight-extrabold);
}

    .cta-btn-primary:hover {
        background: var(--background-gradient-cta-hover);
        color: var(--text-white);
        border-width: var(--border-width-xl);
        box-shadow: 0 var(--space-xs) var(--space-xl) rgba(var(--shadow-color-primary-rgb), 0.18);
    }

.cta-btn .cta-arrow {
    opacity: 0;
    margin-left: 0.2em;
    font-size: var(--font-size-base);
    transform: translateY(6px);
    transition: opacity var(--transition-duration-slow), transform var(--transition-duration-slow);
    display: inline-block;
}

.cta-btn:hover .cta-arrow {
    opacity: 1;
    transform: translateY(0);
}

.about-visual {
    width: 100%;
    max-width: 340px;
    margin: var(--space-none) auto var(--space-unit-lg) auto;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 180px;
    background: linear-gradient(120deg, var(--background-accent-light-alt) 60%, var(--primary-white) 100%);
    border-radius: 18px;
    box-shadow: 0 var(--border-width-lg) var(--space-sm) rgba(var(--shadow-color-accent-rgb), 0.07);
    overflow: hidden;
    position: relative;
    animation: fadeInAbout var(--animation-duration-medium) var(--transition-easing-cubic) both;
}

@media (max-width: 900px) {
    .about-content {
        padding: var(--space-unit-lg) 1vw var(--space-unit-md) 1vw;
    }

    .section-title {
        font-size: var(--font-size-xxxl);
    }
}

@media (max-width: 600px) {
    .about-content {
        padding: 1.2rem 0.2rem var(--space-unit) 0.2rem;
    }

    .section-title {
        font-size: var(--font-size-base);
    }

    .about-visual {
        min-height: 120px;
        max-width: 98vw;
    }
}

.about-row {
    display: flex;
    flex-direction: row;
    gap: var(--space-unit-xl);
    justify-content: center;
    margin-bottom: var(--space-unit-lg);
    position: relative;
    z-index: var(--z-index-low);
}

.about-card {
    background: var(--background-accent-dark-alt);
    border-radius: 20px;
    padding: 2.2rem 1.2rem var(--space-unit-md) 1.2rem;
    box-shadow: 0 var(--space-xxs) var(--space-lg) rgba(var(--shadow-color-accent-rgb), 0.10), 0 var(--border-width-lg) var(--space-xs) rgba(var(--shadow-color-rgb), 0.04);
    margin-bottom: var(--space-unit-sm);
    border-top: var(--space-xxs) solid;
    border-image: var(--border-image-gradient-cta);
    border-right: none;
    border-bottom: none;
    border-left: none;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow var(--transition-duration-slow), border-color var(--transition-duration-slow), transform var(--transition-duration-slow);
    opacity: 0;
    transform: translateY(40px);
}

    .about-card[data-animate="left"].in-view {
        animation: aboutColFadeIn var(--animation-duration-short) var(--animation-delay-sm) var(--transition-easing-cubic) forwards;
    }

    .about-card[data-animate="right"].in-view {
        animation: aboutColFadeIn var(--animation-duration-short) var(--animation-delay-md) var(--transition-easing-cubic) forwards;
    }

    .about-card[data-animate="center"].in-view {
        animation: aboutColFadeIn 1.1s var(--animation-delay-lg) var(--transition-easing-cubic) forwards;
    }

@keyframes aboutColFadeIn {
    to {
        opacity: 1;
        transform: none;
    }
}

.about-card:hover {
    box-shadow: 0 var(--space-sm) 40px rgba(var(--shadow-color-primary-rgb), 0.16), 0 var(--border-width-lg) var(--space-xs) rgba(var(--shadow-color-rgb), 0.07);
    border-top: var(--space-xxs) solid;
    border-image: var(--border-image-gradient-cta-hover);
    transform: translateY(-8px) scale(1.03);
}

    .about-card:hover .about-card-icon svg {
        stroke: var(--primary-blue);
        filter: drop-shadow(0 0 6px var(--primary-blue));
        animation: aboutIconPulse 1.2s infinite alternate;
    }

@keyframes aboutIconPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    100% {
        transform: scale(1.12);
        opacity: 0.85;
    }
}

.about-card-icon-wrap {
    margin-bottom: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-card-icon {
    width: 20%;
    max-width: 90px;
    min-width: 48px;
    height: auto;
    display: block;
    margin: var(--space-none) auto var(--space-unit) auto;
}

    .about-card-icon img, .about-card-icon {
        width: 100%;
        height: auto;
        object-fit: contain;
    }

.about-card-central {
    background: linear-gradient(120deg, var(--background-accent-dark-alt) 80%, var(--background-accent-light-alt) 100%);
    box-shadow: 0 var(--space-xs) 40px rgba(var(--shadow-color-primary-rgb), 0.10), 0 var(--border-width-lg) var(--space-xs) rgba(var(--shadow-color-rgb), 0.04);
    z-index: var(--border-width-xl);
    min-height: 110%;
    margin-top: -20px;
    margin-bottom: -20px;
    filter: drop-shadow(0 var(--space-xs) var(--space-xl) rgba(var(--shadow-color-accent-rgb), 0.10));
    border-top: var(--space-xxs) solid;
    border-image: var(--border-image-gradient-cta-hover);
    position: relative;
}

    .about-card-central .about-card-icon svg {
        stroke: var(--primary-blue);
    }

.about-card h3 {
    color: var(--primary-teal);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-extrabold);
    margin-bottom: var(--space-unit-sm);
    text-align: center;
    letter-spacing: -0.5px;
}

.about-card-central h3 {
    color: var(--primary-blue);
}

@media (max-width: 900px) {
    .about-row {
        flex-direction: column;
        gap: 1.2rem;
    }

    .about-card-central {
        min-height: unset;
        margin-top: var(--space-none);
        margin-bottom: var(--space-none);
    }
}

/* Cart.css */
.cart-container {
    width: 100%;
    margin: var(--space-none) auto;
    margin-top: var(--space-90px);
    padding: var(--space-none) var(--space-lg);
    min-height: calc(100vh - 200px);
    max-width: none;
}

.cart-header {
    text-align: center;
    margin-bottom: 15px;
    position: relative;
    z-index: var(--z-index-default);
}

.cart-title {
    font-size: var(--font-size-2_5-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    margin-bottom: var(--space-unit-sm);
}

.cart-subtitle {
    font-size: var(--font-size-base);
    max-width: 600px;
    margin: var(--space-none) auto;
    color: var(--primary-blue);
}

.cart-main-layout {
    display: flex;
    gap: var(--space-xl);
    align-items: flex-start;
    min-height: calc(100vh - 300px);
    position: relative;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
}

    .cart-main-layout.global-drag-over {
        background: var(--background-overlay-cart-drag);
        border: var(--border-width-lg) dashed var(--border-color-blue-light);
        border-radius: var(--space-md);
        padding: 20px;
    }

.cart-sidebar {
    width: 320px;
    background: var(--background-secondary);
    border: var(--border-width-sm) solid var(--border-color-light-1);
    border-radius: var(--space-md);
    padding: var(--space-lg);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: var(--space-lg);
    flex-shrink: 0;
    max-height: calc(100vh - 200px);
}

.cart-sidebar-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-dark-1);
    margin-bottom: 5px;
    text-align: center;
}

.cart-sidebar-subtitle {
    font-size: var(--font-size-Small);
    color: var(--text-color-light-1);
    margin-bottom: 5px;
    text-align: center;
}

.cart-sidebar-search {
    margin-bottom: 5px;
}

    .cart-sidebar-search input {
        width: 100%;
        padding: var(--space-xs) var(--space-sm);
        border: var(--border-width-sm) solid var(--border-color-light-1);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        background: var(--background-secondary);
    }

.cart-industries-list {
    max-height: calc(100vh - 334px);
    overflow-y: auto;
}

.cart-industry-item {
    padding: var(--space-sm) var(--space-md);
    margin: var(--space-xs) var(--space-none);
    background: var(--background-accent-dark-alt);
    border: var(--border-width-sm) solid var(--border-color-light-1);
    border-radius: var(--radius-md);
    cursor: grab;
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color-dark-2);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

    .cart-industry-item:hover {
        background: var(--background-cart-hover);
        border-color: var(--border-color-light-2);
        transform: translateY(-1px);
    }

    .cart-industry-item.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }

.cart-industry-icon {
    width: 50px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.cart-grid-container {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    min-height: var(--space-none);
    background: var(--background-secondary);
    border-radius: var(--space-md);
    scrollbar-width: none;
}

    .cart-grid-container::-webkit-scrollbar {
        width: var(--space-none);
        background: transparent;
    }

.cart-view-controls {
    position: sticky;
    top: var(--space-none);
    z-index: var(--z-index-low);
    background: var(--background-secondary);
    box-shadow: 0 var(--border-width-lg) var(--space-xs) rgba(20, 184, 166, 0.07);
    border-radius: var(--space-md) var(--space-md) var(--space-none) var(--space-none);
    padding-top: var(--space-xs);
    padding-bottom: var(--space-xs);
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 18px;
    margin-bottom: var(--space-none);
}

.cart-search-section {
    flex: 1;
    max-width: 300px;
}

.cart-search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.cart-search-input {
    width: 100%;
    padding: 10px var(--space-md);
    padding-right: 40px;
    border: var(--border-width-sm) solid var(--platform-border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--background-secondary);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

    .cart-search-input:focus {
        outline: none;
        border-color: var(--primary-teal);
        box-shadow: 0 0 0 var(--border-width-xl) var(--background-overlay-cart-drag);
    }

    .cart-search-input::placeholder {
        color: var(--pagination-hover-border);
    }

.cart-search-clear {
    position: absolute;
    right: var(--space-xs);
    top: 50%;
    transform: translateY(-50%);
    width: var(--space-lg);
    height: var(--space-lg);
    border: none;
    background: var(--search-clear-bg);
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-base);
    font-weight: bold;
    color: var(--search-clear-text);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

    .cart-search-clear:hover {
        background: var(--platform-border-color);
        color: var(--search-clear-text-hover);
    }

.cart-counts-section {
    display: flex;
    gap: var(--space-xs);
    flex-wrap: wrap;
    align-items: center;
}

.cart-count-item {
    display: flex;
    align-items: center;
    gap: var(--radius-sm);
    padding: var(--radius-sm) var(--space-sm);
    background: var(--background-secondary);
    border: var(--border-width-sm) solid var(--border-color-light-1);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
}

.cart-count-icon {
    font-size: var(--font-size-base);
    display: flex;
    align-items: center;
}

.cart-count-icon-img {
    width: var(--space-md);
    height: var(--space-md);
    object-fit: contain;
}

.cart-count-label {
    color: var(--search-clear-text);
    font-weight: var(--font-weight-medium);
}

.cart-count-value {
    color: var(--primary-teal);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-base);
}

.cart-count-item.active {
    background: var(--primary-teal);
    color: var(--text-white);
    border-color: var(--primary-teal);
}

    .cart-count-item.active .cart-count-label,
    .cart-count-item.active .cart-count-value {
        color: var(--text-white);
    }

.cart-pagination-info {
    font-size: 0.875rem;
    color: var(--search-clear-text);
    font-weight: var(--font-weight-medium);
    flex-shrink: 0;
}

.cart-cards-view {
    margin-top: var(--space-xs);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-lg);
    align-items: start;
    margin-bottom: var(--space-lg);
}

.cart-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-md);
    margin: var(--space-xl) var(--space-none);
    padding: var(--space-md);
    background: var(--background-accent-dark-alt);
    border-radius: var(--radius-lg);
    border: var(--border-width-sm) solid var(--border-color-light-1);
}

.cart-pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-md);
    border: var(--border-width-sm) solid var(--platform-border-color);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
}

    .cart-pagination-btn:hover:not(:disabled) {
        background: var(--pagination-hover-bg);
        border-color: var(--pagination-hover-border);
    }

    .cart-pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

.cart-pagination-pages {
    display: flex;
    gap: var(--space-xxs);
}

.cart-pagination-page {
    width: 36px;
    height: 36px;
    border: var(--border-width-sm) solid var(--platform-border-color);
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    justify-content: center;
}

    .cart-pagination-page:hover {
        background: var(--pagination-hover-bg);
        border-color: var(--pagination-hover-border);
    }

    .cart-pagination-page.active {
        background: var(--primary-teal);
        color: var(--text-white);
        border-color: var(--primary-teal);
    }

.cart-card {
    background: var(--background-secondary);
    border: var(--border-width-sm) solid var(--border-color-light-1);
    border-radius: var(--space-md);
    padding: 15px;
    box-shadow: var(--shadow-cart-card);
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: fit-content;
    min-height: 300px;
}

    .cart-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-cart-card-hover);
    }

.cart-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 48px;
    justify-content: center;
}

.cart-card-icon-container {
    background: var(--primary-teal);
    color: var(--text-white);
    border-radius: var(--radius-md);
    padding: var(--space-sm);
    margin-right: var(--space-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xxl);
    min-width: 48px;
    height: 48px;
    flex-shrink: 0;
}

.cart-card-title-container {
    flex: 1;
    min-width: var(--space-none);
}

.cart-card-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--text-color-dark-1);
    margin-bottom: var(--space-xxs);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cart-card-type {
    font-size: 0.875rem;
    color: var(--text-color-light-1);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
}

.cart-platforms-section {
    margin-top: var(--space-unit-sm);
    margin-bottom: var(--space-unit-sm);
    flex-shrink: 0;
}

.cart-platforms-title {
    font-size: 0.9rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-dark-1);
    margin-bottom: var(--space-sm);
}

.cart-platforms-group {
    display: flex;
    gap: var(--space-unit-md);
    margin-top: var(--space-unit-sm);
}

.cart-platform-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-unit-sm);
    background: var(--background-secondary);
    border: var(--border-width-lg) solid var(--platform-border-color);
    border-radius: var(--space-unit-lg);
    padding: 0.4rem 1.2rem 0.4rem 0.8rem;
    font-size: var(--font-size-base);
    color: var(--platform-toggle-color);
    cursor: pointer;
    transition: background var(--transition-duration-base), border var(--transition-duration-base), color var(--transition-duration-base), box-shadow var(--transition-duration-base);
    box-shadow: var(--shadow-platform-toggle);
    outline: none;
}

    .cart-platform-toggle .cart-platform-icon {
        width: 20px;
        height: 20px;
        display: block;
        transition: filter var(--transition-duration-base);
    }

    .cart-platform-toggle .cart-platform-label {
        font-weight: var(--font-weight-medium);
        letter-spacing: 0.01em;
        transition: color var(--transition-duration-base);
    }

    .cart-platform-toggle.active {
        background: var(--background-secondary);
        color: var(--platform-toggle-active);
        border-color: var(--platform-toggle-active);
        box-shadow: var(--shadow-platform-toggle-active);
    }

        .cart-platform-toggle.active .cart-platform-label {
            color: var(--platform-toggle-active);
        }

        .cart-platform-toggle.active .cart-platform-icon {
            filter: none;
        }

    .cart-platform-toggle:hover,
    .cart-platform-toggle:focus-visible {
        border-color: var(--platform-toggle-active);
        color: var(--platform-toggle-active);
        background: var(--platform-toggle-active-bg);
    }

    .cart-platform-toggle:active {
        background: var(--platform-toggle-active-bg-alt);
    }

.cart-industries-section {
    margin-top: var(--space-unit-sm);
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 120px;
}

.cart-industries-title {
    font-size: 0.9rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-dark-1);
    margin-bottom: 0.25rem;
}

.cart-drop-zone {
    min-height: 160px; /* Further increased height for better visibility */
    max-height: 160px;
    overflow-y: auto;
    border: 1.5px dashed var(--platform-border-color);
    border-radius: 10px;
    padding: 10px 12px;
    background: var(--background-accent-dark-alt);
    transition: border-color var(--transition-duration-base), background var(--transition-duration-base);
}

    .cart-drop-zone.active {
        border-color: var(--platform-toggle-active);
        background: var(--platform-toggle-active-bg);
    }

.cart-selected-industries {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.cart-drop-zone::-webkit-scrollbar {
    width: var(--radius-sm);
    background: transparent;
}

.cart-drop-zone::-webkit-scrollbar-thumb {
    background: #e0e7ef;
    border-radius: var(--space-xxs);
}

.cart-drop-zone-empty {
    color: var(--text-color-light-2);
    font-size: var(--font-size-base);
    padding: 0.25rem var(--space-none);
}

.cart-industry-tag {
    display: inline-flex;
    align-items: center;
    background: var(--background-cart-hover);
    border: 1.5px solid var(--border-color-blue-light);
    color: var(--border-color-blue-light);
    border-radius: 16px;
    padding: 3px 9px 3px 8px;
    font-weight: 500;
    font-size: 0.88rem;
    margin: 1px 0;
    cursor: pointer;
    transition: background 0.2s, border-color 0.2s;
}

.cart-remove-industry {
    cursor: pointer;
    margin-left: 6px;
    font-size: 1em;
    font-weight: bold;
    color: var(--text-error);
}

.cart-card-footer {
    margin-top: auto;
    padding-top: var(--space-md);
    border-top: var(--border-width-sm) solid var(--border-color-light-1);
}

.cart-delete-btn {
    background: var(--delete-gradient);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-none);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    display: flex;
    align-items: center;
    gap: var(--radius-sm);
    width: 100%;
    justify-content: center;
}

    .cart-delete-btn:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-delete-hover);
    }

.cart-subscription-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-dark-1);
    margin-bottom: 20px;
    text-align: center;
}

.cart-subscription-option {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    cursor: pointer;
    padding: var(--space-sm) var(--space-md);
    border: var(--border-width-lg) solid var(--border-color-light-1);
    border-radius: var(--radius-md);
    background: var(--background-secondary);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

    .cart-subscription-option.selected {
        border-color: var(--border-color-blue);
        background: var(--background-subscription-selected);
    }

.cart-radio {
    width: var(--space-md);
    height: var(--space-md);
    accent-color: var(--border-color-blue);
}

.cart-subscription-label {
    font-weight: var(--font-weight-semibold);
}

.cart-subscription-discount {
    font-size: var(--font-size-0_75-rem);
    color: var(--text-color-light-1);
}

.cart-action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
}

.cart-add-section {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.cart-add-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-lg);
    background: var(--background-accent-dark-alt);
    color: var(--primary-teal);
    text-decoration: none;
    border: var(--border-width-lg) solid var(--primary-teal);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
    cursor: pointer;
}

    .cart-add-btn:hover {
        background: var(--primary-teal);
        color: var(--text-white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-checkout-hover);
    }

.cart-total-count {
    font-size: 0.875rem;
    color: var(--search-clear-text);
    font-weight: var(--font-weight-medium);
    padding: var(--space-xs) var(--space-sm);
    background: var(--pagination-hover-bg);
    border-radius: var(--radius-sm);
    border: var(--border-width-sm) solid var(--search-clear-bg);
    white-space: nowrap;
}

.cart-checkout-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-xl);
    background: var(--primary-teal);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
}

    .cart-checkout-btn:hover {
        background: var(--checkout-hover-bg);
        transform: translateY(-2px);
        box-shadow: var(--shadow-checkout-hover);
    }

.cart-empty {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-color-light-1);
    grid-column: 1 / -1;
}

.cart-empty-icon {
    font-size: var(--space-unit-xxxl);
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.cart-empty-text {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-xs);
}

.cart-empty-subtext {
    font-size: var(--font-size-base);
    color: var(--text-color-light-2);
}

.cart-success {
    text-align: center;
    padding: 80px 20px;
    color: var(--text-color-dark-1);
    grid-column: 1 / -1;
}

.cart-success-icon {
    font-size: var(--space-unit-xxxl);
    margin-bottom: var(--space-lg);
    color: var(--success-icon-color);
}

.cart-success-title {
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-md);
    color: var(--text-color-dark-1);
}

.cart-success-text {
    font-size: var(--font-size-base);
    color: var(--text-color-dark-2);
    line-height: 1.6;
}

@media (max-width: 1200px) {
    .cart-cards-view {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (max-width: 1024px) {
    .cart-main-layout {
        flex-direction: column;
        gap: var(--space-lg);
    }

    .cart-sidebar {
        width: 100%;
        position: static;
        max-height: 300px;
    }

    .cart-grid-container {
        max-height: 300px;
    }

    .cart-cards-view {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    .cart-view-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-sm);
    }

    .cart-search-section {
        max-width: none;
    }

    .cart-counts-section {
        justify-content: center;
        flex-wrap: wrap;
    }

    .cart-pagination {
        flex-wrap: wrap;
        gap: var(--space-sm);
    }

    .cart-add-new-section {
        margin-left: var(--space-none);
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .cart-container {
        padding: var(--space-none) var(--space-md);
    }

    .cart-title {
        font-size: var(--font-size-xxxl);
    }

    .cart-cards-view {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .cart-subscription-options {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .cart-action-buttons {
        flex-direction: column;
        gap: var(--space-md);
        align-items: stretch;
    }

    .cart-add-section {
        justify-content: center;
    }

    .cart-sidebar {
        max-height: 250px;
    }

    .cart-industries-list {
        max-height: 180px;
    }

    .cart-pagination {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .cart-pagination-pages {
        justify-content: center;
    }

    .cart-view-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-sm);
    }

    .cart-search-section {
        max-width: none;
    }

    .cart-counts-section {
        justify-content: center;
        gap: var(--radius-sm);
    }

    .cart-count-item {
        padding: var(--space-xxs) var(--space-xs);
        font-size: var(--font-size-0_75-rem);
    }

    .cart-add-new-section {
        justify-content: center;
    }

    .cart-add-new-btn {
        width: 100%;
        justify-content: center;
        padding: var(--space-sm) var(--space-md);
    }
}

@media (max-width: 480px) {
    .cart-container {
        padding: var(--space-none) var(--space-sm);
    }

    .cart-title {
        font-size: 1.75rem;
    }

    .cart-subtitle {
        font-size: var(--font-size-base);
    }

    .cart-sidebar {
        padding: var(--space-md);
    }

    .cart-card {
        padding: var(--space-sm);
        min-height: 250px;
    }

    .cart-view-controls {
        padding: var(--space-sm);
    }

    .cart-pagination-page {
        width: var(--space-xl);
        height: var(--space-xl);
        font-size: var(--font-size-0_8-rem);
    }

    .cart-search-input {
        padding: var(--radius-sm) 10px;
        padding-right: var(--space-xl);
        font-size: var(--font-size-0_75-rem);
    }

    .cart-search-clear {
        width: 18px;
        height: 18px;
        font-size: var(--font-size-0_75-rem);
    }

    .cart-counts-section {
        gap: var(--space-xxs);
    }

    .cart-count-item {
        padding: var(--border-width-xl) var(--radius-sm);
        font-size: var(--font-size-0_75-rem);
        gap: var(--border-width-xl);
    }

    .cart-count-value {
        font-size: 0.875rem;
    }

    .cart-count-icon {
        font-size: var(--font-size-0_75-rem);
    }

    .cart-count-icon-img {
        width: var(--space-sm);
        height: var(--space-sm);
    }
}

.cart-checkbox-container, .cart-checkbox, .cart-checkbox-label {
    display: none !important;
}

.cart-delete-icon-btn {
    display: none;
    background: none;
    border: none;
    padding: 0.2rem;
    margin-left: var(--space-unit-sm);
    cursor: pointer;
    border-radius: var(--radius-full);
    transition: background var(--transition-duration-fast);
    align-items: center;
    justify-content: center;
    height: var(--space-xl);
    width: var(--space-xl);
    outline: none;
    box-shadow: none;
}

    .cart-card:hover .cart-delete-icon-btn,
    .cart-delete-icon-btn:focus-visible {
        display: flex;
    }

.cart-delete-icon {
    width: 30px;
    height: 25px;
    display: block;
    color: var(--text-error);
    filter: none;
    transition: color var(--transition-duration-fast);
}

.cart-add-new-section {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.cart-add-new-btn {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 10px var(--space-md);
    background: var(--primary-teal);
    color: var(--text-white);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-semibold);
    font-size: 0.875rem;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
    border: var(--border-width-lg) solid var(--primary-teal);
}

    .cart-add-new-btn:hover {
        background: var(--background-secondary);
        color: var(--primary-teal);
        transform: translateY(-2px);
        box-shadow: var(--shadow-add-hover);
    }

    .cart-add-new-btn.hover {
        background: var(--background-secondary);
        color: var(--primary-teal);
        transform: translateY(-2px);
        box-shadow: var(--shadow-add-hover);
    }

.cart-action-buttons.center {
    justify-content: center;
}

.cart-subscription-section.split {
    display: flex;
    gap: 18px;
    align-items: flex-start;
    justify-content: space-between;
    background: var(--background-secondary);
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(20, 184, 166, 0.07), 0 1px 3px rgba(0,0,0,0.03);
    padding: 18px 18px 12px 18px;
    margin-top: 28px;
    margin-bottom: var(--space-lg);
}

.cart-subscription-left {
    flex: 0 0 68%;
    max-width: 68%;
}

.cart-subscription-right {
    flex: 0 0 32%;
    max-width: 32%;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: var(--space-none) 15px var(--space-none) var(--space-none);
    min-height: 120px;
    background: var(--background-accent-dark-alt);
    border-radius: var(--space-none) 10px 10px var(--space-none);
}

.cart-subscription-title {
    font-size: var(--font-size-1_18-rem);
    font-weight: var(--font-weight-bold);
    color: var(--primary-teal);
    margin-bottom: var(--space-md);
    text-align: left;
    letter-spacing: -0.5px;
}

.cart-subscription-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.cart-subscription-option {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    cursor: pointer;
    padding: 10px var(--space-sm);
    border: var(--border-width-lg) solid var(--border-color-light-1);
    border-radius: 10px;
    background: var(--background-accent-dark-alt);
    transition: all var(--transition-duration-fast) cubic-bezier(.4,0,.2,1);
    box-shadow: 0 1px 4px rgba(20, 184, 166, 0.03);
    min-width: 120px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    position: relative;
}

    .cart-subscription-option.selected {
        border-color: var(--primary-teal);
        background: var(--background-accent-light-alt);
        box-shadow: 0 2px 8px rgba(20, 184, 166, 0.07);
    }

    .cart-subscription-option:hover {
        border-color: var(--primary-teal-dark);
        background: var(--platform-toggle-active-bg);
        box-shadow: 0 1.5px 6px rgba(20, 184, 166, 0.07);
    }

.cart-radio {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-teal);
    margin-right: var(--space-xxs);
}

.cart-subscription-label {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    color: var(--text-tertiary);
}

.cart-subscription-discount {
    font-size: var(--font-size-0_78-rem);
    color: var(--primary-teal-dark);
    font-weight: var(--font-weight-medium);
    margin-top: var(--border-width-sm);
}

.cart-subscription-right {
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    min-width: 120px;
    background: var(--background-accent-dark-alt);
    border-radius: var(--space-none) 10px 10px var(--space-none);
    box-shadow: none;
}

.cart-checkout-summary {
    font-size: var(--font-size-base);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
    margin-bottom: 10px;
    margin-top: var(--space-none);
    line-height: 1.4;
}

.cart-checkout-btn.cart-add-new-btn {
    width: 100%;
    font-size: var(--font-size-base);
    padding: 10px var(--space-none);
    border-radius: var(--radius-md);
    margin-top: var(--space-xxs);
    font-weight: var(--font-weight-bold);
    background: var(--checkout-gradient);
    color: var(--text-white);
    border: none;
    box-shadow: 0 1.5px 6px rgba(20, 184, 166, 0.07);
    transition: background var(--transition-duration-fast), color var(--transition-duration-fast);
}

    .cart-checkout-btn.cart-add-new-btn:hover {
        background: var(--checkout-gradient-hover);
        color: var(--text-white);
    }

@media (max-width: 900px) {
    .cart-subscription-section.split {
        flex-direction: column;
        gap: 10px;
        padding: var(--space-sm) var(--space-xxs) 10px var(--space-xxs);
    }

    .cart-subscription-left, .cart-subscription-right {
        max-width: 100%;
        flex: 1 1 100%;
        padding-top: var(--space-none);
        border-radius: 10px;
        border-left: none;
    }

    .cart-subscription-right {
        padding: 10px var(--space-none) var(--space-none) var(--space-none);
        min-width: unset;
        background: var(--background-accent-dark-alt);
        border-radius: 10px;
        box-shadow: none;
    }
}

.cart-checkout-paper {
    width: 100%;
    padding: 18px;
    padding-bottom: 14px;
    border-radius: var(--radius-lg);
    background: var(--background-secondary);
    box-shadow: 0 2px 12px rgba(20, 184, 166, 0.10), 0 1.5px 6px rgba(0,0,0,0.04);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: var(--space-none);
}

.cart-checkout-subtext {
    font-size: var(--font-size-0_92-rem);
    color: var(--primary-teal-dark);
    margin-top: var(--space-xs);
    font-weight: var(--font-weight-medium);
    opacity: 0.85;
}

.cart-checkout-btn-mui {
    margin-top: var(--space-sm);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    border-radius: var(--radius-md);
    box-shadow: 0 1.5px 6px rgba(20, 184, 166, 0.07);
    max-width: 220px;
    width: 100%;
    align-self: center;
}

.cart-details-row {
    display: flex;
    gap: var(--space-lg);
    width: 100%;
    margin-top: 28px;
    margin-bottom: var(--space-lg);
}

.cart-subscription-section {
    flex: 0 0 60%;
    max-width: 60%;
}

.cart-checkout-section {
    flex: 0 0 38.5%;
    max-width: 38.5%;
    background: var(--background-secondary);
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

@media (max-width: 900px) {
    .cart-details-row {
        flex-direction: column;
        gap: var(--space-sm);
        margin-top: var(--space-md);
        margin-bottom: var(--space-sm);
    }

    .cart-subscription-section, .cart-checkout-section {
        max-width: 100%;
        padding: var(--space-sm) var(--radius-sm) 10px var(--radius-sm);
        border-radius: 10px;
    }

    .cart-subscription-section {
        flex: 1 1 100%;
    }

    .cart-checkout-section {
        flex: 1 1 100%;
    }
}

.cart-checkout-paper.compact-flex {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background: var(--background-secondary);
    border-radius: 14px;
    height: 100%;
}

.cart-checkout-title {
    font-size: var(--font-size-1_18-rem);
    font-weight: var(--font-weight-bold);
    color: var(--primary-teal);
    margin-bottom: var(--space-md);
    text-align: left;
    letter-spacing: -0.5px;
}

.cart-checkout-left {
    flex: 1 1 60%;
    min-width: var(--space-none);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.cart-checkout-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.cart-checkout-btn-mui {
    margin-top: var(--space-none);
    max-width: 180px;
    min-width: 140px;
    width: auto;
    align-self: flex-end;
}

@media (max-width: 900px) {
    .cart-checkout-paper.compact-flex {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .cart-checkout-left, .cart-checkout-right {
        width: 100%;
        justify-content: center;
        align-items: center;
    }

    .cart-checkout-btn-mui {
        align-self: center;
        width: 100%;
        max-width: 220px;
    }
}

/* Contact.css */
.contact-section {
    padding: var(--space-unit-xxxl) var(--space-none);
    background: var(--primary-white);
    position: relative;
    overflow: hidden;
}

.contact-advanced-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-unit-xxxl);
    max-width: 1200px;
    margin: var(--space-none) auto;
    padding: var(--space-none) var(--space-unit-lg);
    position: relative;
    z-index: var(--z-index-default);
}

.contact-info-col {
    padding-right: var(--space-unit-lg);
}

.contact-title {
    font-size: var(--font-size-2_5-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    margin-bottom: var(--space-unit-md);
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.contact-desc {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--text-color-dark-2);
    margin-bottom: var(--space-unit-lg);
}

    .contact-desc b {
        color: var(--tertiary-navy);
        font-weight: var(--font-weight-semibold);
    }

.contact-phones {
    display: flex;
    flex-direction: column;
    gap: var(--space-unit);
    margin: var(--space-unit-lg) var(--space-none);
}

.contact-phone-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: var(--font-size-1_2-rem);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-teal);
    text-decoration: none;
    padding: var(--space-unit) var(--space-unit-md);
    background: var(--background-contact-phone);
    border-radius: var(--radius-lg);
    border: var(--border-width-lg) solid transparent;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
}

    .contact-phone-link:hover {
        background: var(--background-contact-phone-hover);
        border-color: var(--primary-teal);
        transform: translateY(-2px);
        box-shadow: var(--shadow-contact-phone-hover);
    }

.contact-phone-icon {
    font-size: var(--font-size-1_4-rem);
    color: var(--primary-teal);
}

.contact-desc-bottom {
    font-size: var(--font-size-base);
    color: var(--text-color-light-1);
    margin-top: var(--space-unit-lg);
}

.contact-form-col {
    background: var(--background-secondary);
    padding: var(--space-unit-xxl);
    border-radius: 20px;
    box-shadow: var(--shadow-contact-form);
    border: var(--border-width-sm) solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.contact-form-advanced {
    display: flex;
    flex-direction: column;
    gap: var(--space-unit-md);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-unit-md);
}

    .form-row .form-floating-group.full-width {
        grid-column: 1 / -1;
    }

.form-floating-group {
    position: relative;
    display: flex;
    flex-direction: column;
}

    .form-floating-group input,
    .form-floating-group select {
        width: 100%;
        padding: 1.25rem var(--space-unit) 0.75rem;
        border: var(--border-width-lg) solid var(--border-color-light-1);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-base);
        background: var(--background-secondary);
        transition: all var(--transition-duration-slow) var(--transition-easing-ease);
        position: relative;
        z-index: var(--z-index-default);
    }

        .form-floating-group input:focus,
        .form-floating-group select:focus {
            outline: none;
            border-color: var(--primary-teal);
            box-shadow: var(--shadow-contact-input-focus);
            transform: translateY(-1px);
        }

        .form-floating-group input.invalid,
        .form-floating-group select.invalid {
            border-color: var(--text-error);
            box-shadow: var(--shadow-contact-input-invalid);
        }

    .form-floating-group label {
        position: absolute;
        top: 1.25rem;
        left: var(--space-unit);
        font-size: var(--font-size-base);
        color: var(--text-color-light-1);
        transition: all var(--transition-duration-slow) var(--transition-easing-ease);
        pointer-events: none;
        z-index: var(--z-index-low);
        background: var(--background-secondary);
        padding: var(--space-none) 0.25rem;
    }

    .form-floating-group input:focus + label,
    .form-floating-group input:not(:placeholder-shown) + label,
    .form-floating-group select:focus + label,
    .form-floating-group select:not([value=""]) + label {
        top: -0.5rem;
        font-size: 0.875rem;
        color: var(--primary-teal);
        font-weight: var(--font-weight-semibold);
        background: var(--background-secondary);
        padding: var(--space-none) var(--space-unit-sm);
        border-radius: var(--space-xxs);
    }

    .form-floating-group input:focus.invalid + label,
    .form-floating-group input.invalid:not(:placeholder-shown) + label {
        color: var(--text-error);
    }

.form-error {
    color: var(--text-error);
    font-size: 0.875rem;
    margin-top: var(--space-unit-sm);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideIn var(--transition-duration-slow) var(--transition-easing-ease);
}

    .form-error::before {
        content: '⚠';
        font-size: var(--font-size-0_75-rem);
    }

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.checkbox-row {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.custom-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--space-unit);
    cursor: pointer;
    padding: var(--space-unit);
    border-radius: var(--radius-lg);
    border: var(--border-width-lg) solid transparent;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
    position: relative;
}

    .custom-checkbox-label:hover {
        background: rgba(46, 192, 203, 0.05);
        border-color: rgba(46, 192, 203, 0.2);
    }

    .custom-checkbox-label.invalid {
        border-color: rgba(229, 62, 62, 0.3);
        background: rgba(229, 62, 62, 0.05);
    }

    .custom-checkbox-label input[type="checkbox"] {
        display: none;
    }

.custom-checkbox {
    width: var(--space-lg);
    height: var(--space-lg);
    border: var(--border-width-lg) solid var(--border-color-light-2);
    border-radius: var(--radius-sm);
    background: var(--background-secondary);
    position: relative;
    flex-shrink: 0;
    margin-top: 0.125rem;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
}

.custom-checkbox-label:hover .custom-checkbox {
    border-color: var(--primary-teal);
    background: var(--background-contact-phone);
}

.custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox {
    background: var(--primary-teal);
    border-color: var(--primary-teal);
}

    .custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var(--text-white);
        font-size: 14px;
        font-weight: bold;
    }

.checkbox-main-label {
    font-weight: var(--font-weight-medium);
    color: var(--tertiary-navy);
    line-height: var(--line-height-base);
}

    .checkbox-main-label a {
        color: var(--primary-teal);
        text-decoration: none;
        font-weight: var(--font-weight-semibold);
    }

        .checkbox-main-label a:hover {
            text-decoration: underline;
        }

.checkbox-desc {
    font-size: 0.875rem;
    color: var(--text-color-light-1);
    line-height: 1.6;
    margin-left: 2.5rem;
    padding: 0.75rem;
    background: var(--background-accent-dark-alt);
    border-radius: var(--radius-md);
    border-left: var(--border-width-xl) solid var(--border-color-light-1);
}

    .checkbox-desc.small {
        font-size: var(--font-size-0_8-rem);
        max-height: 80px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--border-color-light-2) transparent;
    }

        .checkbox-desc.small::-webkit-scrollbar {
            width: var(--radius-sm);
        }

        .checkbox-desc.small::-webkit-scrollbar-track {
            background: transparent;
        }

        .checkbox-desc.small::-webkit-scrollbar-thumb {
            background: var(--border-color-light-2);
            border-radius: var(--border-width-xl);
        }

    .checkbox-desc a {
        color: var(--primary-teal);
        text-decoration: none;
        font-weight: var(--font-weight-medium);
    }

        .checkbox-desc a:hover {
            text-decoration: underline;
        }

.contact-submit-btn {
    grid-column: 1 / -1;
    background: var(--gradient-submit);
    color: var(--text-white);
    border: none;
    padding: 1.25rem var(--space-unit-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-top: var(--space-unit);
    position: relative;
    overflow: hidden;
}

    .contact-submit-btn::before {
        content: '';
        position: absolute;
        top: var(--space-none);
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-submit-shine);
        transition: left var(--animation-duration-micro) var(--transition-easing-ease);
    }

    .contact-submit-btn:hover::before {
        left: 100%;
    }

    .contact-submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(46, 192, 203, 0.3);
        background: var(--gradient-submit-hover);
    }

    .contact-submit-btn:active {
        transform: translateY(0);
    }

    .contact-submit-btn:disabled {
        background: var(--border-color-light-2);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

        .contact-submit-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

.submit-arrow {
    font-size: var(--font-size-1_2-rem);
    transition: transform var(--transition-duration-slow) var(--transition-easing-ease);
}

.contact-submit-btn:hover .submit-arrow {
    transform: translateX(4px);
}

@media (max-width: 1024px) {
    .contact-advanced-grid {
        gap: var(--space-unit-xxl);
        padding: var(--space-none) var(--space-unit-md);
    }

    .contact-info-col {
        padding-right: var(--space-unit);
    }

    .contact-form-col {
        padding: var(--space-unit-xl);
    }
}

@media (max-width: 768px) {
    .contact-advanced-grid {
        grid-template-columns: 1fr;
        gap: var(--space-unit-lg);
    }

    .contact-info-col {
        padding-right: var(--space-none);
        text-align: center;
    }

    .contact-title {
        font-size: var(--font-size-xxxl);
    }

    .contact-phones {
        align-items: center;
    }

    .contact-form-col {
        padding: var(--space-unit-lg);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--space-unit);
    }

    .contact-submit-btn {
        padding: var(--space-unit) var(--space-unit-md);
        font-size: var(--font-size-base);
    }
}

@media (max-width: 480px) {
    .contact-section {
        padding: var(--space-unit-xxxl) var(--space-none);
    }

    .contact-advanced-grid {
        padding: var(--space-none) var(--space-unit);
    }

    .contact-form-col {
        padding: var(--space-unit-md);
    }

    .contact-title {
        font-size: 1.75rem;
    }

    .contact-desc {
        font-size: var(--font-size-base);
    }
}
/* Faq.css */
.faq-section {
    padding: var(--space-unit-xxxl) var(--space-none);
}

.faq-container {
    max-width: 900px;
    margin: var(--space-none) auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-unit-lg) var(--space-unit-xl);
}

@media (max-width: 800px) {
    .faq-container {
        grid-template-columns: 1fr;
        gap: 1.2rem;
    }
}

.section-title {
    color: var(--primary-teal);
}

.section-subtitle {
    color: var(--primary-blue);
}

.faq-item {
    border-bottom: var(--border-width-sm) solid var(--secondary-gray-light);
    padding: var(--space-unit-md) var(--space-none);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

    .faq-question h4 {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        color: var(--tertiary-navy);
        margin: var(--space-none);
    }

.faq-icon {
    font-size: var(--font-size-xxl);
    color: var(--primary-teal);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--animation-duration-micro) cubic-bezier(0, 1, 0, 1), padding var(--animation-duration-micro) var(--transition-easing-ease);
}

.faq-item.open .faq-answer {
    max-height: 1000px; /* Large enough to fit any content */
    padding-top: var(--space-unit);
    transition: max-height var(--animation-duration-faq) cubic-bezier(1, 0, 1, 0), padding var(--animation-duration-micro) var(--transition-easing-ease);
}

.faq-answer p {
    margin: var(--space-none);
    line-height: var(--line-height-base);
    color: var(--secondary-teal-dark);
}

/* Hero.css */
.hero-section {
    position: relative;
    padding: 3.5rem var(--space-none);
    text-align: center;
    color: var(--primary-white);
    overflow: hidden;
    min-height: 48vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-background {
    position: absolute;
    top: var(--space-none);
    left: var(--space-none);
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--gradient-hero);
    background-size: 400% 400%;
    animation: gradientAnimation var(--animation-duration-hero) var(--transition-easing-ease) infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.hero-content {
    max-width: 800px;
    margin: var(--space-none) auto;
    position: relative;
    z-index: var(--z-index-default);
}

.hero-headline {
    font-size: var(--font-size-hero);
    font-weight: var(--font-weight-extrabold);
    line-height: var(--line-height-sm);
    margin-bottom: var(--space-unit-md);
    text-shadow: var(--shadow-hero);
}

.hero-subheadline {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-light);
    max-width: 600px;
    margin: var(--space-none) auto var(--space-unit-xl);
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: var(--space-unit);
}

.button-secondary {
    background-color: transparent;
    color: var(--primary-white);
    padding: 0.75rem var(--space-unit-md);
    border-radius: var(--radius-xxl);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
    border: var(--border-width-lg) solid var(--primary-white);
}

    .button-secondary:hover {
        background-color: var(--primary-white);
        color: var(--primary-teal);
        transform: translateY(-2px);
    }

/* Industries.css */
.industries-section {
    padding: var(--space-unit-xxxl) var(--space-none) var(--space-unit-lg) var(--space-none);
    overflow: hidden;
    margin-bottom: var(--space-none);
}




.industries-header-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.7rem;
    margin-top: var(--space-unit-sm);
}


/* Modulescard.css */
.module-card {
    background-color: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-unit-md);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: var(--shadow-module-card);
    transition: transform var(--transition-duration-slow) var(--transition-easing-ease), box-shadow var(--transition-duration-slow) var(--transition-easing-ease);
    border: var(--border-width-sm) solid var(--tertiary-gray-light);
    position: relative;
    max-width: 370px;
    min-width: 280px;
    margin: var(--space-none) auto;
}

    .module-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-module-card-hover);
    }

.card-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--space-unit);
}

.card-icon-container {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    border-radius: var(--radius-md);
    padding: var(--space-unit-sm);
    margin-right: var(--space-unit);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.card-title {
    font-size: var(--font-size-1_4-rem);
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.25rem;
}

.card-category {
    font-size: var(--font-size-0_8-rem);
    color: var(--secondary-teal-dark);
    text-transform: uppercase;
    font-weight: var(--font-weight-medium);
}

.card-description {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--tertiary-navy);
    flex-grow: 1;
    margin-bottom: var(--space-unit);
}

.card-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: var(--space-unit);
    border-top: var(--border-width-sm) solid var(--tertiary-gray-light);
}

.card-actions {
    display: flex;
    gap: 0.7rem;
    justify-content: center;
    width: 100%;
}

.add-to-cart-btn, .try-now-btn {
    background-color: var(--secondary-gray-light);
    color: var(--primary-teal);
    border: var(--border-width-sm) solid var(--border-color-button);
    padding: 0.6rem 1.1rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    transition: background var(--transition-duration-base), color var(--transition-duration-base), transform var(--transition-duration-base);
    line-height: var(--line-height-sm);
}

    .add-to-cart-btn:hover {
        background: var(--primary-teal);
        color: var(--primary-white);
        transform: translateY(-2px);
    }

.try-now-btn {
    background: var(--primary-blue);
    color: var(--primary-white);
}

    .try-now-btn:hover {
        background: var(--primary-teal-dark);
        color: var(--primary-white);
        transform: translateY(-2px);
    }

.suite-tags {
    position: absolute;
    top: 1.2rem;
    right: 1.2rem;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-unit-sm);
    z-index: var(--border-width-xl);
}

.card-tags {
    position: absolute;
    top: 0.8rem;
    right: 1.2rem;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-unit-sm);
    z-index: var(--border-width-xl);
}

.module-tag-badge {
    position: absolute;
    top: var(--space-none);
    right: var(--space-none);
    left: auto;
    background: var(--tag-color-gold);
    color: var(--text-tertiary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    padding: 0.1rem var(--space-unit-sm);
    border-top-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
    z-index: var(--z-index-mid);
    box-shadow: var(--shadow-sm);
    margin: var(--space-none);
    text-align: center;
    white-space: nowrap;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

    .module-tag-badge.tag-bestseller {
        background: var(--tag-color-orange);
        color: var(--tertiary-black);
    }

    .module-tag-badge.tag-top-rated {
        background: var(--tag-color-yellow);
        color: var(--text-tertiary);
    }

    .module-tag-badge.tag-mobile-ready {
        background: var(--tag-color-cyan);
        color: var(--text-white);
    }

    .module-tag-badge.tag-included-in-suits {
        background: var(--tag-color-purple);
        color: var(--text-white);
    }

    .module-tag-badge.tag-oem-focused {
        background: var(--text-tertiary);
        color: var(--text-white);
    }

    .module-tag-badge.tag-trending {
        background: var(--tag-color-green);
        color: var(--text-white);
    }

    .module-tag-badge.tag-best-value {
        background: var(--tag-color-violet);
        color: var(--text-white);
    }

    .module-tag-badge.tag-enterprise-ready {
        background: var(--tag-color-ocean);
        color: var(--text-white);
    }

    .module-tag-badge.tag-ai-powered {
        background: var(--primary-blue);
        color: var(--text-white);
    }

    .module-tag-badge.tag-analytics-reporting {
        background: var(--tag-color-teal);
        color: var(--text-white);
    }

    .module-tag-badge.tag-customer-favorite {
        background: var(--tag-color-amber);
        color: var(--text-tertiary);
    }

/* Modules.css */
.modules-section {
    width: 100vw;
    background: var(--background-secondary);
    padding: var(--space-none);
    margin: var(--space-none);
    min-height: 100vh;
    overflow-x: hidden;
}

.modules-flex {
    display: flex;
    width: 100vw;
    max-width: 100vw;
    margin: var(--space-none);
    padding: var(--space-none);
}

.modules-main {
    width: 100%;
    max-width: 1280px;
    margin: var(--space-none) auto;
    padding: var(--space-none) 2.5vw var(--space-unit-xxxl) 2.5vw;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    min-height: 100vh;
    overflow: visible;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-unit-xxl);
    flex-shrink: 0;
}

.section-title {
    font-size: var(--font-size-2_5-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    margin-bottom: var(--space-unit-sm);
}

.section-subtitle {
    font-size: var(--font-size-base);
    max-width: 600px;
    margin: var(--space-none) auto;
    color: var(--primary-blue);
}

.filter-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: var(--space-unit-xxl);
}

.filter-btn {
    background-color: var(--primary-white);
    color: var(--secondary-teal-dark);
    border: var(--border-width-sm) solid var(--secondary-gray-light);
    padding: 0.6rem 1.2rem;
    border-radius: var(--radius-xxl);
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-duration-slow) var(--transition-easing-ease);
}

    .filter-btn:hover {
        background-color: var(--primary-teal);
        color: var(--primary-white);
        border-color: var(--primary-teal);
    }

    .filter-btn.active {
        background-color: var(--primary-teal);
        color: var(--primary-white);
        border-color: var(--primary-teal);
        box-shadow: var(--shadow-md-alt);
    }

.grid-center-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
}

.modules-grid {
    min-height: var(--space-none);
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    grid-auto-rows: min-content;
    gap: var(--space-unit-xl);
    max-width: 100%;
    width: 100%;
    scrollbar-width: none;
    -ms-overflow-style: none;
    justify-items: center;
}

    .modules-grid::-webkit-scrollbar {
        display: none;
    }

@media (min-width: 1400px) {
    .modules-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1800px) {
    .modules-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 900px) {
    .modules-grid {
        max-width: 100vw;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

.modules-grid.expanded {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

    .modules-grid.expanded::-webkit-scrollbar {
        display: none !important;
    }

    .modules-grid.expanded::-webkit-scrollbar-track {
        background: var(--background-scrollbar-track);
        border-radius: var(--space-xxs);
    }

    .modules-grid.expanded::-webkit-scrollbar-thumb {
        background: var(--background-scrollbar-thumb);
        border-radius: var(--space-xxs);
    }

        .modules-grid.expanded::-webkit-scrollbar-thumb:hover {
            background: var(--background-scrollbar-thumb-hover);
        }

.modules-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-unit-lg);
    gap: var(--space-unit-lg);
    flex-shrink: 0;
}

.filter-toggle-btn {
    background: var(--primary-teal);
    color: var(--primary-white);
    border: none;
    border-radius: var(--radius-xxl);
    padding: 0.7rem 1.6rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(46,192,203,0.13);
    transition: background var(--transition-duration-base), transform var(--transition-duration-base);
}

    .filter-toggle-btn:hover {
        background: var(--primary-teal-dark);
        transform: translateY(-2px);
    }

.view-more-row {
    display: flex;
    justify-content: center;
    margin-top: var(--space-unit-lg);
    flex-shrink: 0;
}

.view-more-btn {
    background: var(--primary-blue);
    color: var(--primary-white);
    border: none;
    border-radius: var(--radius-xxl);
    padding: 0.85rem 2.2rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(15,95,220,0.13);
    transition: background var(--transition-duration-base), transform var(--transition-duration-base);
}

    .view-more-btn:hover {
        background: var(--primary-teal-dark);
        transform: translateY(-2px);
    }

@media (max-width: 900px) {
    .modules-main {
        padding: var(--space-none) 2vw var(--space-unit-lg) 2vw;
        min-height: 100vh;
    }

    .modules-grid {
        gap: 1.2rem;
    }

        .modules-grid.expanded {
            max-height: calc(100vh - 150px);
        }
}

.solutions-main-flex {
    display: flex;
    height: 100vh;
    min-height: var(--space-none);
    align-items: stretch;
    overflow: hidden;
}

.sidebar-advanced-filter {
    height: 100%;
    min-height: var(--space-none);
    overflow-y: auto;
    flex-shrink: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

    .sidebar-advanced-filter::-webkit-scrollbar {
        display: none;
    }

.modules-grid::-webkit-scrollbar {
    width: var(--space-xs);
}

.modules-grid::-webkit-scrollbar-track {
    background: var(--background-scrollbar-track);
    border-radius: var(--space-xxs);
}

.modules-grid::-webkit-scrollbar-thumb {
    background: var(--background-scrollbar-thumb);
    border-radius: var(--space-xxs);
}

    .modules-grid::-webkit-scrollbar-thumb:hover {
        background: var(--background-scrollbar-thumb-hover);
    }

.modules-grid.single-card {
    display: flex !important;
    justify-content: center;
    align-items: flex-start;
    max-width: 400px;
    margin: var(--space-none) auto;
}

/* Partners.css */
.partners-section {
    padding: var(--space-unit-xxxl) var(--space-none);
    background: var(--background-gradient-about);
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1.2rem var(--space-unit-md);
    align-items: center;
    justify-items: center;
    padding: var(--space-unit-xl) var(--space-none) var(--space-unit-md) var(--space-none);
    width: 100%;
    max-width: 1200px;
    margin: var(--space-none) auto;
}

.partners-row {
    display: none;
}

.partner-logo-wrap {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
    max-width: 180px;
    height: 72px;
    background: var(--primary-white);
    border-radius: var(--space-md);
    box-shadow: 0 2px 12px rgba(var(--shadow-color-accent-rgb),0.07);
    border: var(--border-width-md) solid var(--secondary-gray-light);
    transition: box-shadow 0.25s, transform 0.25s, background var(--transition-duration-base), border var(--transition-duration-base);
    cursor: pointer;
    opacity: 0;
    animation: logoFadeIn var(--animation-duration) var(--transition-easing-cubic) forwards;
    scroll-snap-align: center;
    padding: 0.7rem 1.2rem;
    overflow: visible;
}

    .partner-logo-wrap:nth-child(1) {
        animation-delay: 0.1s;
    }

    .partner-logo-wrap:nth-child(2) {
        animation-delay: 0.18s;
    }

    .partner-logo-wrap:nth-child(3) {
        animation-delay: 0.26s;
    }

    .partner-logo-wrap:nth-child(4) {
        animation-delay: 0.34s;
    }

    .partner-logo-wrap:nth-child(5) {
        animation-delay: 0.42s;
    }

    .partner-logo-wrap:nth-child(6) {
        animation-delay: 0.50s;
    }

    .partner-logo-wrap:nth-child(7) {
        animation-delay: 0.58s;
    }

    .partner-logo-wrap:nth-child(8) {
        animation-delay: 0.66s;
    }

    .partner-logo-wrap:nth-child(9) {
        animation-delay: 0.74s;
    }

    .partner-logo-wrap:nth-child(10) {
        animation-delay: 0.82s;
    }

    .partner-logo-wrap:nth-child(11) {
        animation-delay: 0.90s;
    }

    .partner-logo-wrap:nth-child(12) {
        animation-delay: 0.98s;
    }

    .partner-logo-wrap:nth-child(13) {
        animation-delay: 1.06s;
    }

    .partner-logo-wrap:nth-child(14) {
        animation-delay: 1.14s;
    }

    .partner-logo-wrap:nth-child(15) {
        animation-delay: 1.22s;
    }

    .partner-logo-wrap:nth-child(16) {
        animation-delay: 1.30s;
    }

    .partner-logo-wrap:nth-child(17) {
        animation-delay: 1.38s;
    }

    .partner-logo-wrap:nth-child(18) {
        animation-delay: 1.46s;
    }

    .partner-logo-wrap:nth-child(19) {
        animation-delay: 1.54s;
    }

    .partner-logo-wrap:nth-child(20) {
        animation-delay: 1.62s;
    }

    .partner-logo-wrap:nth-child(21) {
        animation-delay: 1.70s;
    }

    .partner-logo-wrap:nth-child(22) {
        animation-delay: 1.78s;
    }

    .partner-logo-wrap:nth-child(23) {
        animation-delay: 1.86s;
    }

@keyframes logoFadeIn {
    to {
        opacity: 1;
    }
}

.partner-logo-wrap:hover {
    box-shadow: 0 8px 32px rgba(var(--shadow-color-accent-rgb),0.18);
    transform: scale(1.10) translateY(-4px);
    background: var(--primary-white);
    border: var(--border-width-md) solid var(--primary-teal);
    z-index: var(--z-index-mid);
}

.partner-logo {
    max-width: 110px;
    max-height: 54px;
    margin: var(--space-none) auto;
    display: block;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.04));
    transition: filter var(--transition-duration-base);
    object-fit: contain;
}

.partner-logo-wrap:hover .partner-logo {
    filter: drop-shadow(0 4px 12px rgba(var(--shadow-color-accent-rgb),0.18));
}

.partner-tooltip {
    position: absolute;
    left: 50%;
    bottom: 110%;
    transform: translateX(-50%) scale(0.95);
    background: var(--primary-teal);
    color: var(--primary-white);
    padding: 0.4rem var(--space-unit);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-base);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-duration-base), transform var(--transition-duration-base);
    box-shadow: 0 2px 8px rgba(var(--shadow-color-accent-rgb),0.12);
    z-index: var(--z-index-mid);
}

.partner-logo-wrap:hover .partner-tooltip {
    opacity: 1;
    transform: translateX(-50%) scale(1);
}

.partner-testimonial {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, 16px);
    min-width: 260px;
    max-width: 320px;
    background: var(--background-secondary);
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(var(--shadow-color-accent-rgb),0.18), 0 2px 8px rgba(0,0,0,0.07);
    padding: 1.2rem 1.3rem var(--space-unit) 1.3rem;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s, transform 0.25s;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-unit-sm);
    z-index: var(--z-index-mid);
}

.partner-logo-wrap:hover .partner-testimonial {
    opacity: 1;
    pointer-events: auto;
    transform: translate(-50%, 0);
}

.testimonial-quote-icon {
    color: var(--primary-teal);
    font-size: var(--font-size-base);
    margin-bottom: 0.2rem;
}

.testimonial-quote {
    font-size: var(--font-size-base);
    font-style: italic;
    color: var(--text-tertiary);
    margin-bottom: 0.2rem;
}

.testimonial-author {
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    font-size: var(--font-size-1_01-rem);
}

.testimonial-title {
    font-size: var(--font-size-base);
    color: var(--text-color-light-2);
    margin-top: -0.2rem;
}

@media (max-width: 1200px) {
    .partners-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 900px) {
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-unit) 0.7rem;
    }

    .partner-logo-wrap {
        min-width: 100px;
        max-width: 120px;
        height: 44px;
        padding: 0.4rem 0.7rem;
    }

    .partner-logo {
        max-width: 70px;
        max-height: 32px;
    }
}

@media (max-width: 600px) {
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.7rem var(--space-unit-sm);
    }
}

.section-title {
    color: var(--primary-teal);
}

.section-subtitle {
    color: var(--primary-blue);
}

/* Pricing.css */
.pricing-section {
    padding: var(--space-unit-xxxl) var(--space-none) var(--space-unit-lg) var(--space-none);
}

.billing-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-unit);
    margin-bottom: var(--space-unit-xxl);
}

.toggle-label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--secondary-teal-dark);
    transition: color var(--transition-duration-slow) var(--transition-easing-ease);
}

    .toggle-label.active {
        color: var(--tertiary-navy);
        font-weight: var(--font-weight-bold);
    }

.save-badge {
    background-color: var(--tag-color-gold);
    color: var(--tertiary-navy);
    font-size: var(--font-size-0_75-rem);
    font-weight: var(--font-weight-bold);
    padding: 0.2rem var(--space-unit-sm);
    border-radius: var(--space-xxs);
    margin-left: var(--space-unit-sm);
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

    .switch input {
        opacity: 0;
        width: var(--space-none);
        height: var(--space-none);
    }

.slider {
    position: absolute;
    cursor: pointer;
    top: var(--space-none);
    left: var(--space-none);
    right: var(--space-none);
    bottom: var(--space-none);
    background-color: var(--secondary-gray-light);
    transition: var(--animation-transition-slider);
}

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: var(--space-xxs);
        bottom: var(--space-xxs);
        background-color: var(--primary-white);
        transition: var(--animation-transition-slider);
    }

input:checked + .slider {
    background-color: var(--primary-teal);
}

    input:checked + .slider:before {
        transform: translateX(26px);
    }

.slider.round {
    border-radius: 34px;
}

    .slider.round:before {
        border-radius: var(--radius-full);
    }

.pricing-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-unit-xl);
    justify-items: center;
    align-items: stretch;
    padding: var(--space-none) 4.5rem var(--space-unit-lg) 4.5rem;
    margin-bottom: var(--space-unit-xl);
    width: 100%;
    overflow-x: visible;
}

.pricing-grid {
    display: contents;
}

@media (max-width: 900px) {
    .pricing-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
        padding: var(--space-none) var(--space-unit) var(--space-unit-lg) var(--space-unit);
    }
}

@media (max-width: 600px) {
    .pricing-row {
        grid-template-columns: 1fr;
        gap: 1.1rem;
        padding: var(--space-none) 0.7rem var(--space-unit-lg) 0.7rem;
    }
}

.feature-comparison-section {
    background: var(--primary-white);
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(var(--shadow-color-accent-rgb),0.08);
    margin: var(--space-none) auto;
    max-width: 1400px;
    padding: var(--space-unit-xl) 3.5rem;
    overflow-x: auto;
}

.feature-comparison-title {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--tertiary-navy);
    margin-bottom: var(--space-unit-md);
    text-align: left;
}

.feature-comparison-table-wrapper {
    overflow-x: auto;
}

.feature-comparison-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: var(--space-none);
    min-width: 700px;
    background: transparent;
    text-align: left;
}

    .feature-comparison-table th,
    .feature-comparison-table td {
        padding: var(--space-unit) var(--space-unit-md);
        text-align: left;
        font-size: var(--font-size-base);
        border-bottom: var(--border-width-sm) solid var(--tertiary-gray-light);
        background: var(--primary-white);
    }

        .feature-comparison-table td.user-limit-cell {
            text-align: left !important;
            font-weight: var(--font-weight-semibold);
            color: var(--tertiary-navy);
        }

    .feature-comparison-table th {
        position: sticky;
        top: var(--space-none);
        background: var(--primary-white);
        z-index: var(--z-index-low);
        font-weight: var(--font-weight-bold);
        color: var(--primary-teal);
        border-bottom: var(--border-width-lg) solid var(--primary-teal);
        text-align: left;
    }

    .feature-comparison-table td {
        text-align: left;
        vertical-align: middle;
    }

    .feature-comparison-table tr:last-child td {
        border-bottom: none;
    }

@media (max-width: 700px) {
    .feature-comparison-table th,
    .feature-comparison-table td {
        padding: 0.7rem var(--space-unit-sm);
        font-size: var(--font-size-base);
    }

    .feature-comparison-title {
        font-size: var(--font-size-base);
    }
}

.container-fluid {
    width: 100vw;
    max-width: 100vw;
    margin: var(--space-none);
    padding: var(--space-none);
}

.feature-comparison-section.full-width {
    max-width: 100vw;
    border-radius: var(--space-none);
    margin: var(--space-none);
    padding: var(--space-unit-md) 4.5rem;
}

.feature-comparison-accordion {
    width: 100%;
    margin: var(--space-none) auto;
    background: var(--primary-white);
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(var(--shadow-color-accent-rgb),0.08);
    overflow: hidden;
}

.feature-category {
    border-bottom: var(--border-width-md) solid var(--tertiary-gray-light);
}

.feature-category-header {
    display: flex;
    align-items: center;
    gap: 0.7rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--tertiary-navy);
    background: var(--background-feature-category);
    border: none;
    border-radius: 18px 18px var(--space-none) var(--space-none);
    padding: 1.2rem var(--space-unit-md);
    margin-bottom: var(--space-none);
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background var(--transition-duration-fast), color var(--transition-duration-fast);
    box-shadow: 0 2px 8px rgba(var(--shadow-color-accent-rgb),0.04);
}

    .feature-category-header span {
        display: flex;
        align-items: center;
        gap: var(--space-unit-sm);
    }

    .feature-category-header:hover {
        background: var(--secondary-teal-extra-light);
    }

    .feature-category-header.open {
        background: var(--primary-teal);
        color: var(--text-white);
    }

        .feature-category-header.open span {
            color: var(--text-white);
        }

.feature-category-body {
    background: var(--primary-white);
    padding: var(--space-none) var(--space-unit-lg) var(--space-unit-lg) var(--space-unit-lg);
    animation: fadeInAccordion var(--transition-duration-slow);
}

@keyframes fadeInAccordion {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: none;
    }
}

@media (max-width: 900px) {
    .container-fluid, .feature-comparison-section.full-width {
        padding: var(--space-none) var(--space-unit-sm) var(--space-unit-lg) var(--space-unit-sm);
    }

    .feature-category-header {
        font-size: var(--font-size-base);
        padding: var(--space-unit) 1.2rem var(--space-unit) var(--space-unit);
    }

    .feature-category-body {
        padding: var(--space-none) var(--space-unit-sm) 1.2rem var(--space-unit-sm);
    }
}

.plan-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 0.7rem;
    min-height: 2.2rem;
}

.plan-name-with-icon {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: var(--font-size-1_22-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
}

@media (max-width: 700px) {
    .plan-header-row {
        min-height: 1.7rem;
        margin-bottom: var(--space-unit-sm);
    }

    .plan-name-with-icon {
        font-size: 1.05rem;
        gap: 0.4rem;
    }
}

/* PricingCard.css */
.pricing-card {
    background-color: var(--primary-white);
    border: var(--border-width-sm) solid var(--secondary-gray-light);
    border-radius: var(--radius-lg);
    padding: var(--space-unit-md) 1.2rem 1.2rem 1.2rem;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    max-width: 320px;
    min-height: 420px;
    height: 420px;
    justify-content: flex-start;
    align-items: stretch;
    transition: transform var(--transition-duration-slow) var(--transition-easing-ease), box-shadow var(--transition-duration-slow) var(--transition-easing-ease);
    position: relative;
    box-shadow: 0 2px 12px rgba(var(--shadow-color-accent-rgb),0.07);
}

    .pricing-card.popular {
        border-color: var(--primary-teal);
        transform: scale(1.05);
        box-shadow: 0 10px 40px rgba(var(--shadow-color-accent-rgb), 0.2);
        z-index: var(--z-index-mid);
    }

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-teal);
    color: var(--primary-white);
    padding: 0.4rem var(--space-unit);
    border-radius: var(--radius-xxl);
    font-size: var(--font-size-0_8-rem);
    font-weight: var(--font-weight-bold);
}

.pricing-card-header {
    text-align: center;
    padding-bottom: var(--space-unit);
    border-bottom: var(--border-width-sm) solid var(--tertiary-gray-light);
}

.plan-name {
    font-size: var(--font-size-1_15-rem);
    font-weight: var(--font-weight-bold);
    color: var(--primary-teal);
    margin-bottom: 0.7rem;
}

.plan-price {
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-extrabold);
    color: var(--tertiary-navy);
    margin-bottom: 0.1rem;
    text-align: left;
    min-height: 3.2rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.price-amount {
    font-size: 2.2rem;
    font-weight: var(--font-weight-extrabold);
    color: var(--tertiary-navy);
}

.price-period, .price-contact {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--secondary-teal-dark);
    margin-left: 0.4rem;
}

.price-contact {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
}

.features-list {
    list-style: none;
    margin: var(--space-unit) var(--space-none) var(--space-unit-sm) var(--space-none);
    flex-grow: 1;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.7rem;
    font-size: var(--font-size-0_93-rem);
}

.feature-icon {
    color: var(--primary-teal);
    font-size: var(--font-size-base);
    margin-right: 0.6rem;
}

.pricing-card-footer {
    text-align: center;
    margin-top: auto;
    padding-top: 0.7rem;
}

.button-pricing {
    width: 100%;
    padding: 0.7rem;
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    border: var(--border-width-lg) solid var(--primary-teal);
}

    .button-pricing.button-primary {
        background-color: var(--primary-teal);
        color: var(--primary-white);
    }

    .button-pricing.button-secondary {
        background-color: transparent;
        color: var(--primary-teal);
    }

        .button-pricing.button-secondary:hover {
            background-color: var(--primary-teal);
            color: var(--primary-white);
        }

.plan-quickview {
    list-style: none;
    margin: var(--space-unit) var(--space-none) 0.7rem var(--space-none);
    padding: var(--space-none);
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.plan-quickview-item {
    display: flex;
    align-items: center;
    font-size: var(--font-size-1_01-rem);
    color: var(--tertiary-navy);
    gap: 0.4rem;
    font-weight: var(--font-weight-medium);
}

.plan-quickview-label {
    margin-right: 0.3rem;
    font-weight: var(--font-weight-medium);
    color: var(--tertiary-navy);
}

.plan-quickview-value {
    font-weight: var(--font-weight-bold);
    color: var(--tertiary-black);
    margin-left: 0.1rem;
}

.pricing-summary-card {
    background: var(--background-secondary);
    border-radius: 10px;
    border: var(--border-width-md) solid var(--secondary-gray-light);
    padding: 1.3rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 340px;
    min-width: 220px;
    min-height: 380px;
    height: 100%;
    margin: var(--space-none) var(--space-unit-sm);
    position: relative;
    transition: border var(--transition-duration-fast), background var(--transition-duration-fast);
    box-shadow: none;
    overflow: visible;
    word-break: break-word;
    box-sizing: border-box;
}

    .pricing-summary-card.popular {
        background: var(--background-accent-light-alt);
        border: var(--border-width-lg) solid var(--primary-teal);
    }

.most-popular-badge {
    position: absolute;
    top: var(--space-none);
    right: var(--space-none);
    left: auto;
    transform: none;
    background: var(--tag-color-gold);
    color: var(--tertiary-navy);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    padding: 0.45rem 1.2rem;
    border-radius: 0 7px 0 7px;
    letter-spacing: 0.01em;
    text-transform: uppercase;
    z-index: var(--border-width-xl);
    border: var(--border-width-lg) solid var(--primary-white);
    box-shadow: var(--shadow-sm);
}

.plan-name {
    font-size: var(--font-size-1_22-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    margin-bottom: 0.3rem;
    margin-top: 0.2rem;
    text-align: left;
}

.plan-summary {
    font-size: var(--font-size-1_01-rem);
    color: #333;
    margin-bottom: 0.7rem;
    text-align: left;
    min-height: 38px;
}

.plan-quickview {
    list-style: none;
    margin: 0.7rem var(--space-none);
    padding: var(--space-none);
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.plan-quickview-item {
    display: flex;
    align-items: center;
    font-size: var(--font-size-1_01-rem);
    color: var(--tertiary-navy);
    gap: 0.4rem;
    font-weight: var(--font-weight-medium);
    text-align: left;
}

.plan-quickview-label {
    margin-right: 0.3rem;
    font-weight: var(--font-weight-medium);
    color: var(--tertiary-navy);
}

.plan-quickview-value {
    font-weight: var(--font-weight-bold);
    color: var(--tertiary-black);
    margin-left: 0.1rem;
}

.button-pricing.button-primary {
    margin-top: auto;
    background: var(--primary-teal);
    color: var(--text-white);
    font-weight: var(--font-weight-bold);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    border: none;
    width: 100%;
    padding: 0.8rem var(--space-none);
    transition: background var(--transition-duration-fast), color var(--transition-duration-fast);
    box-shadow: none;
}

    .button-pricing.button-primary:hover {
        background: var(--primary-teal);
        color: var(--text-white);
    }

@media (max-width: 700px) {
    .plan-header-row {
        margin-top: 0.1rem;
    }
}

.plan-separator {
    border: none;
    border-top: var(--border-width-md) solid var(--secondary-gray-light);
    margin: var(--space-unit) var(--space-none) 0.7rem var(--space-none);
    width: 100%;
}

@media (max-width: 900px) {
    .pricing-summary-card {
        width: 100%;
        min-width: 160px;
        max-width: 98vw;
        min-height: 380px;
        padding: 1.1rem 0.7rem;
    }

    .plan-price {
        font-size: var(--font-size-base);
        min-height: 2.2rem;
    }
}

@media (max-width: 600px) {
    .pricing-summary-card {
        width: 100%;
        min-width: 120px;
        max-width: 99vw;
        min-height: 380px;
        padding: 0.7rem 0.3rem;
    }

    .plan-summary, .plan-header-row, .plan-price, .plan-quickview, .button-pricing {
        word-break: break-word;
        font-size: var(--font-size-base);
    }
}

.about-title {
    font-size: var(--font-size-2_5-rem);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-teal);
    margin-bottom: var(--space-unit-md);
    /* font-family: 'HCLTechRoobert', sans-serif; */
}

.about-description {
    font-size: var(--font-size-base);
    color: var(--tertiary-navy);
    /* font-family: 'HCLTechRoobert', sans-serif; */
    line-height: var(--line-height-base);
}

/* Custom thin dark teal scrollbar for all scrollable areas */
::-webkit-scrollbar {
    width: var(--radius-sm);
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-teal-dark);
    border-radius: var(--space-xxs);
}

    ::-webkit-scrollbar-thumb:hover {
        background: var(--scrollbar-thumb-color-hover);
    }

::-webkit-scrollbar-track {
    background: transparent;
}


* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-teal-dark) transparent;
}

    *,
    *::before,
    *::after {
        box-sizing: border-box;
        margin: var(--space-none);
        padding: var(--space-none);
    }

html {
    scroll-behavior: smooth;
}

body {
    background-color: var(--primary-white);
    color: var(--tertiary-navy);
    line-height: 1.6;
    font-weight: var(--font-weight-normal);
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

img, picture, svg, video {
    display: block;
    max-width: 100%;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-sm);
}


.container {
    width: 100%;
    max-width: 1440px;
    margin: var(--space-none) auto;
    /* padding-left: 3.5vw;
  padding-right: 3.5vw; */
}

@media (max-width: 900px) {
    .container {
        padding-left: var(--space-unit-md);
        padding-right: var(--space-unit-md);
    }
}

/* Global Button Styles */
.btn {
    display: inline-block;
    /* font-family: inherit; */
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-xxl);
    padding: var(--space-0_7-rem) var(--space-1_6-rem);
    border: none;
    cursor: pointer;
    transition: background var(--transition-duration-color), color var(--transition-duration-color), box-shadow var(--transition-duration-color), transform var(--transition-duration-color);
    box-shadow: var(--shadow-btn);
}

.btn-primary {
    background: var(--primary-teal);
    color: var(--primary-white);
}

    .btn-primary:hover, .btn-primary:focus {
        background: var(--primary-teal-dark);
        color: var(--primary-white);
        transform: var(--transform-btn-hover);
    }

.btn-secondary {
    background: var(--primary-white);
    color: var(--primary-teal);
    border: var(--border-width-lg) solid var(--primary-teal);
}

    .btn-secondary:hover, .btn-secondary:focus {
        background: var(--primary-teal);
        color: var(--primary-white);
        border-color: var(--primary-teal-dark);
        transform: var(--transform-btn-hover);
    }

/* Small Button Modifier */
.btn-sm {
    padding: var(--space-0_45-rem) var(--space-unit);
    font-size: var(--font-size-base);
    border-radius: var(--radius-button-sm);
    box-shadow: none;
}

/* SidebarAdvancedfilter.css */
.sidebar-overlay {
    position: fixed;
    top: var(--space-none);
    left: var(--space-none);
    width: 100vw;
    height: 100vh;
    background: var(--background-sidebar-overlay);
    z-index: var(--z-index-sidebar-overlay);
    transition: opacity var(--transition-duration-slow) var(--transition-easing-cubic);
    opacity: 1;
}

.sidebar-advanced-filter {
    background: var(--background-secondary);
    min-width: 220px;
    max-width: 270px;
    width: 100%;
    box-shadow: 0 2px 8px 0 rgba(0,64,128,0.04);
    border-radius: 14px;
    padding: 18px 14px 12px 14px;
    display: flex;
    flex-direction: column;
    gap: 14px;
    transition: transform var(--transition-duration-slow) var(--transition-easing-cubic), box-shadow var(--transition-duration-slow);
    z-index: var(--z-index-sidebar-advanced);
    height: 100%;
    border: var(--border-width-sm) solid var(--border-color-light-1);
}

    .sidebar-advanced-filter.desktop {
        position: sticky;
        top: 80px;
        height: calc(100vh - 80px);
    }

    .sidebar-advanced-filter.mobile {
        position: fixed;
        top: var(--space-none);
        left: var(--space-none);
        height: 100vh;
        min-width: 80vw;
        max-width: 90vw;
        border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
        box-shadow: 0 0 32px 0 rgba(0,0,0,0.18);
        transform: translateX(-110%);
        transition: transform var(--transition-duration-slow) var(--transition-easing-cubic);
    }

    .sidebar-advanced-filter.open {
        transform: translateX(0) !important;
        box-shadow: 0 0 32px 0 rgba(0,0,0,0.18);
    }

        .sidebar-advanced-filter.open.desktop {
            position: sticky !important;
            top: 0px !important;
            height: 100% !important;
            z-index: var(--z-index-sidebar-advanced);
        }

.sidebar-header h3 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--filter-text-color);
    display: flex;
    align-items: center;
    gap: var(--radius-sm);
    letter-spacing: 0;
}

.sidebar-close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-base);
    color: var(--tag-color-ocean);
    cursor: pointer;
    margin-left: var(--space-xxs);
    line-height: 1;
    padding: var(--space-none) var(--border-width-lg);
    transition: color var(--transition-duration-base);
}

    .sidebar-close-btn:hover {
        color: var(--tag-color-cyan);
    }

.sidebar-content {
    flex: 1 1 auto;
    overflow-y: auto;
    height: 100%;
    max-height: 100%;
    padding-bottom: var(--space-xxs);
}

.filter-group:not(:last-child) {
    border-bottom: var(--border-width-sm) solid var(--border-color-filter);
    padding-bottom: 10px;
}

.filter-toggle-btn {
    background: var(--primary-teal);
    color: var(--primary-white);
    border: none;
    border-radius: var(--radius-xxl);
    padding: 0.7rem 1.6rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(var(--shadow-color-accent-rgb),0.13);
    transition: background var(--transition-duration-base), transform var(--transition-duration-base);
}

    .filter-toggle-btn:hover {
        background: var(--primary-teal-dark);
        transform: translateY(-2px);
    }

.view-more-row {
    display: flex;
    justify-content: center;
    margin-top: var(--space-unit-lg);
    flex-shrink: 0;
}

.view-more-btn {
    background: var(--primary-blue);
    color: var(--primary-white);
    border: none;
    border-radius: var(--radius-xxl);
    padding: 0.85rem 2.2rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(var(--shadow-color-primary-rgb),0.13);
    transition: background var(--transition-duration-base), transform var(--transition-duration-base);
}

    .view-more-btn:hover {
        background: var(--primary-teal-dark);
        transform: translateY(-2px);
    }

.solutions-section {
    width: 100vw;
    background: var(--primary-white);
    padding: var(--space-none);
    margin: var(--space-none);
}

.solutions-main {
    width: 100vw;
    max-width: 100vw;
    margin: var(--space-none) auto;
    padding: var(--space-none) 2vw var(--space-unit-xxxl) 2vw;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.solutions-header-row {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin-bottom: var(--space-unit-xl);
    gap: var(--space-unit-lg);
}

.solutions-tabs-row.center-align {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 18px;
    margin: var(--space-none) auto var(--space-lg) auto;
    width: 100%;
    max-width: 900px;
}

.solutions-tabs {
    display: flex;
    gap: var(--space-xs);
}

.solutions-tab {
    background: var(--background-filter-pill);
    color: var(--tag-color-ocean);
    border: none;
    border-radius: var(--radius-xl);
    padding: 10px 28px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
}

    .solutions-tab.active {
        background: var(--tag-color-cyan);
        color: var(--text-white);
        box-shadow: 0 2px 12px 0 rgba(0,128,198,0.08);
    }

@media (max-width: 900px) {
    .solutions-main {
        padding: var(--space-none) 0.5vw var(--space-unit-lg) 0.5vw;
    }

    .solutions-header-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1.2rem;
    }

    .solutions-tabs {
        justify-content: center;
        gap: var(--space-unit-sm);
        padding: 0.2rem;
    }

    .solutions-tab {
        padding: 0.6rem 1.2rem;
        font-size: var(--font-size-base);
    }
}

.solutions-filter-card {
    width: 100%;
    background: var(--primary-white);
    border-radius: var(--radius-xl);
    box-shadow: 0 6px 32px rgba(var(--shadow-color-accent-rgb),0.10);
    margin-bottom: var(--space-unit-xl);
    padding: 2.2rem 2vw 1.2rem 2vw;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 1.2rem;
}

.solutions-filter-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1.2rem;
    flex-wrap: wrap;
    margin-bottom: var(--space-unit-sm);
}

.filter-toggle-btn {
    background: var(--primary-teal);
    color: var(--primary-white);
    border: none;
    border-radius: var(--radius-xxl);
    padding: 0.6rem var(--space-unit-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(var(--shadow-color-accent-rgb),0.13);
    transition: background var(--transition-duration-base), transform var(--transition-duration-base);
}

    .filter-toggle-btn:hover {
        background: var(--primary-teal-dark);
        transform: translateY(-2px);
    }

.solutions-result-count {
    font-size: var(--font-size-base);
    color: var(--primary-teal);
    font-weight: var(--font-weight-semibold);
    margin: var(--space-none) var(--space-unit-sm);
}

.clear-filters-btn {
    background: var(--secondary-gray-light);
    color: var(--tertiary-navy);
    border: none;
    border-radius: var(--radius-xxl);
    padding: 0.6rem var(--space-unit-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
}

    .clear-filters-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

/* .clear-filters-btn:not(:disabled):hover {
  background: var(--primary-teal);
  color: var(--primary-white);
} */

@media (max-width: 900px) {
    .solutions-filter-card {
        padding: 1.2rem 1vw 0.7rem 1vw;
        border-radius: var(--radius-lg);
        gap: 0.7rem;
    }

    .solutions-filter-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-unit-sm);
    }
}

.solutions-main-flex {
    display: flex;
    height: 100vh;
    min-height: var(--space-none);
    align-items: stretch;
    overflow: hidden;
}

.solutions-content {
    flex: 1 1 0;
    min-width: var(--space-none);
    width: 100%;
    display: flex;
    flex-direction: column;
}

.modern-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 18px;
    margin: var(--space-none) auto var(--space-xl) auto;
    width: 100%;
    max-width: 900px;
}

.toggle-group {
    display: flex;
    background: var(--background-filter-pill);
    border-radius: var(--space-xl);
    box-shadow: 0 2px 8px rgba(0,128,198,0.04);
    padding: var(--space-xxs);
    gap: var(--space-none);
}

.toggle-btn {
    background: none;
    border: none;
    color: var(--tag-color-ocean);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    border-radius: 28px;
    padding: 10px var(--space-xl);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    margin: var(--space-none) var(--border-width-lg);
}

    .toggle-btn.active {
        background: var(--tag-color-cyan);
        color: var(--text-white);
        box-shadow: 0 2px 12px 0 rgba(0,128,198,0.08);
    }

.sidebar-toggle-btn {
    display: flex;
    align-items: center;
    background: var(--tag-color-cyan);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-xl);
    padding: 10px var(--space-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    margin-left: var(--space-sm);
    box-shadow: 0 2px 12px 0 rgba(0,128,198,0.08);
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    gap: var(--radius-sm);
}

    .sidebar-toggle-btn:hover {
        background: var(--tag-color-ocean);
    }

@media (max-width: 900px) {
    .modern-toggle {
        flex-direction: column;
        gap: var(--space-sm);
        margin-bottom: 18px;
    }

    .toggle-btn {
        padding: 10px 18px;
        font-size: var(--font-size-base);
    }
}

@media (min-width: 901px) {
    .sidebar-advanced-filter.desktop {
        position: sticky !important;
        top: 100px !important;
        height: calc(100vh - 100px) !important;
        min-height: 400px;
        z-index: var(--z-index-sidebar-advanced);
    }
}

.suites-grid,
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-xl);
    width: 100%;
    margin: 0 auto;
    transition: max-width var(--transition-duration-slow), padding var(--transition-duration-slow);
    padding: var(--space-none) var(--space-md) var(--space-xl) var(--space-md);
    scrollbar-width: none;
    -ms-overflow-style: none;
}

    .suites-grid.grid-expanded,
    .modules-grid.grid-expanded {
        display: grid;
        grid-template-columns: repeat(4, minmax(260px, 1fr));
        width: 100vw !important;
        max-width: 100vw !important;
        margin: var(--space-none) !important;
        padding: var(--space-none) var(--space-md) var(--space-xl) var(--space-md);
        background: none;
    }

@media (max-width: 1200px) {
    .suites-grid.grid-expanded, .modules-grid.grid-expanded {
        grid-template-columns: repeat(2, minmax(260px, 1fr));
    }
}

@media (max-width: 700px) {
    .suites-grid.grid-expanded, .modules-grid.grid-expanded {
        grid-template-columns: 1fr;
    }
}

/* --- START OF FINAL REFACTORED CSS --- */
.filter-label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--filter-text-color);
    margin-bottom: var(--radius-sm);
    letter-spacing: var(--space-none);
    display: flex;
    align-items: center;
    gap: var(--radius-sm);
}

.filter-icon {
    font-size: var(--font-size-base);
    color: var(--tag-color-cyan);
    margin-right: var(--border-width-lg);
}

.filter-pills {
    display: flex;
    flex-wrap: wrap;
    gap: var(--radius-sm);
    background: none;
    border-radius: var(--space-none);
    padding: var(--space-none);
    margin-bottom: var(--space-none);
    box-shadow: none;
}

.filter-pill {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--tag-color-ocean);
    background: var(--background-filter-pill);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--radius-sm) 14px;
    margin: var(--border-width-sm) var(--border-width-lg) var(--border-width-sm) var(--space-none);
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    min-width: var(--space-none);
}

    .filter-pill.selected {
        background: var(--tag-color-cyan);
        color: var(--text-white);
    }

.filter-search {
    width: 100%;
    padding: 7px 10px;
    border-radius: var(--radius-md);
    border: var(--border-width-sm) solid var(--border-color-light-1);
    font-size: var(--font-size-base);
    background: var(--background-secondary);
    font-weight: var(--font-weight-normal);
    margin-bottom: var(--space-unit-sm);
}

.filter-link {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    text-align: left;
    padding: var(--space-xxs) 10px;
    margin: var(--space-none) var(--space-none) var(--border-width-lg) var(--space-none);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
    width: 100%;
    display: block;
}

    .filter-link.selected,
    .filter-link:focus,
    .filter-link:hover {
        background: var(--tag-color-cyan);
        color: var(--text-white);
    }

.industry-image-thumb {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    max-width: 40px;
    max-height: 40px;
    display: inline-block;
    margin-right: var(--space-sm);
    vertical-align: middle;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: var(--radius-md);
    background-color: var(--background-feature-category);
    box-sizing: border-box;
}

.industry-checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-tertiary);
    margin-bottom: var(--radius-sm);
    cursor: pointer;
    padding: var(--space-xxs) var(--space-none);
    min-height: 48px;
    width: 100%;
}

    .industry-checkbox-label input[type="checkbox"],
    .industry-image-thumb {
        flex-shrink: 0;
    }

.industry-label-text {
    white-space: normal;
    word-break: break-word;
    flex: 1 1 0%;
    min-width: var(--space-none);
    display: block;
}

.industry-checkbox-label input[type="checkbox"] {
    accent-color: var(--tag-color-cyan);
    margin-right: var(--border-width-lg);
}

.industry-checkbox-label:hover {
    background: var(--background-accent-light);
    color: var(--tag-color-cyan);
    border-radius: var(--radius-sm);
}

.industry-checkbox-label input[type="checkbox"]:checked + .industry-icon,
.industry-checkbox-label input[type="checkbox"]:checked ~ span {
    color: var(--tag-color-cyan);
}

.solution-type-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-xxs);
}

.solution-type-radio-label {
    display: flex;
    align-items: center;
    gap: 7px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-tertiary);
    margin-bottom: var(--border-width-lg);
    cursor: pointer;
    padding: var(--border-width-lg) var(--space-none);
}

    .solution-type-radio-label input[type="radio"] {
        accent-color: var(--tag-color-cyan);
        margin-right: var(--border-width-lg);
    }

    .solution-type-radio-label:hover {
        background: var(--background-accent-light);
        color: var(--tag-color-cyan);
        border-radius: var(--radius-sm);
    }

    .solution-type-radio-label input[type="radio"]:checked + span {
        color: var(--tag-color-cyan);
    }

.sidebar-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--space-unit-xxxl);
    margin-top: var(--space-md);
}

.clear-filters-btn {
    color: var(--text-error);
    font-weight: var(--font-weight-semibold);
    background: none;
    border: none;
    padding: var(--space-none);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: color var(--transition-duration-base);
    text-align: left;
    text-decoration: underline;
}

    .clear-filters-btn:hover {
        color: var(--error-hover-color) !important;
    }

.result-count {
    font-size: var(--font-size-base);
    color: var(--tag-color-ocean);
    font-weight: var(--font-weight-medium);
    text-align: left;
}

.industry-icon {
    width: 18px;
    height: 18px;
    margin-right: var(--space-xxs);
    vertical-align: middle;
    object-fit: contain;
    display: inline-block;
}

.industry-search-box {
    width: 100%;
    padding: 7px 10px;
    border-radius: var(--radius-md);
    border: var(--border-width-sm) solid var(--border-color-light-1);
    font-size: var(--font-size-base);
    margin-bottom: var(--radius-sm);
    background: var(--background-secondary);
    font-weight: var(--font-weight-normal);
}

.industry-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-xxs);
    max-height: 245px;
    overflow-y: auto;
}

.industry-pill {
    display: flex;
    align-items: center;
    gap: var(--radius-sm);
    background: var(--background-filter-pill);
    color: var(--tag-color-ocean);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--radius-sm) 10px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background var(--transition-duration-base), color var(--transition-duration-base);
}

    .industry-pill.selected {
        background: var(--tag-color-cyan);
        color: var(--text-white);
    }

@media (max-width: 900px) {
    .sidebar-advanced-filter.desktop {
        display: none !important;
    }

    .sidebar-advanced-filter.mobile {
        display: block;
        min-width: 70vw;
        max-width: 90vw;
        border-radius: var(--space-none) 14px 14px var(--space-none);
        box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
        height: 100%;
        padding: 14px var(--radius-sm) 10px 10px;
    }
}

@media (min-width: 901px) {
    .solutions-main-flex {
        align-items: stretch !important;
    }

    .sidebar-advanced-filter.desktop {
        position: static !important;
        top: auto !important;
        height: auto !important;
        min-height: var(--space-none) !important;
        align-self: stretch !important;
        display: flex;
        flex-direction: column;
        flex: 0 0 270px;
        max-width: 270px;
        width: 100%;
    }

    .sidebar-content {
        flex: 1 1 auto;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .sidebar-advanced-filter.mobile {
        display: none !important;
    }

    .sidebar-advanced-filter.desktop {
        position: sticky !important;
        top: 0px !important;
        min-height: 400px;
        z-index: var(--z-index-sidebar-advanced);
        height: 100% !important;
    }
}

.grid-full {
    width: 100% !important;
    max-width: 100vw !important;
    margin-left: var(--space-none) !important;
}

body, html, .solutions-section, .solutions-main-flex, .solutions-content {
    overflow-x: hidden !important;
}

.advanced-filter {
    box-sizing: border-box;
}

.grid-center-wrapper {
    box-sizing: border-box;
}


.about-section {
    width: 100vw;
    background: var(--primary-white);
    padding: var(--space-unit-xxxl) var(--space-none) var(--space-unit-xxl) var(--space-none);
    margin: var(--space-none) auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-container {
    max-width: 900px;
    margin: var(--space-none) auto;
    padding: var(--space-none) var(--space-unit-lg);
    text-align: center;
}
/* FilterSidebar.css */
.filter-sidebar {
    width: var(--sidebar-max-width);
    min-width: var(--sidebar-min-width);
    background: var(--primary-white);
    border-radius: var(--radius-filter-sidebar);
    box-shadow: var(--shadow-filter-sidebar);
    padding: var(--space-unit-lg) var(--space-unit-md) var(--space-unit-md) var(--space-unit-md);
    margin-right: var(--space-unit-lg);
    transition: transform var(--transition-duration-filter-sidebar) var(--transition-easing-cubic), opacity var(--transition-opacity-filter-sidebar);
    transform: var(--transform-sidebar-closed-alt);
    opacity: var(--space-none);
    position: relative;
    z-index: var(--z-index-mid);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
}

    .filter-sidebar.open {
        transform: var(--transform-sidebar-open);
        opacity: var(--z-index-default);
    }

.filter-close-btn {
    position: absolute;
    top: var(--space-unit);
    right: var(--space-unit);
    background: none;
    border: none;
    font-size: var(--font-size-xxxl);
    color: var(--primary-teal);
    cursor: pointer;
    z-index: var(--z-index-mid-high);
    transition: color var(--transition-duration-color);
}

    .filter-close-btn:hover {
        color: var(--primary-teal-dark);
    }

.filter-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--primary-teal);
    margin-bottom: var(--space-0_7-rem);
}

.filter-tags, .filter-industries {
    display: flex;
    flex-direction: column;
    gap: var(--space-unit-sm);
}

.filter-tag, .filter-industry {
    font-size: var(--font-size-base);
    color: var(--tertiary-navy);
    display: flex;
    align-items: center;
    gap: var(--space-unit-sm);
    cursor: pointer;
}

    .filter-tag input, .filter-industry input {
        accent-color: var(--primary-teal);
    }

@media (max-width: 900px) {
    .filter-sidebar {
        width: var(--sidebar-mobile-max-width);
        min-width: var(--space-none);
        margin-right: var(--space-unit-sm);
        padding: var(--space-1_2-rem) var(--space-0_7-rem) var(--space-1_2-rem) var(--space-0_7-rem);
    }
}

.advanced-filter {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--space-unit-lg);
    background: var(--primary-white);
    border-radius: var(--radius-filter-sidebar);
    box-shadow: var(--shadow-filter-sidebar);
    padding: var(--space-unit-md) var(--space-unit-xl);
    margin-bottom: var(--space-1_2-rem);
    margin-top: var(--space-unit-sm);
    position: relative;
    z-index: var(--z-index-low-mid);
}

.filter-group {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}



.filter-pill {
    background: var(--tertiary-gray-light);
    color: var(--tertiary-navy);
    border-radius: var(--radius-xxl);
    padding: var(--space-0_32-rem) var(--space-1_1-rem);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--space-0_4-rem);
    cursor: pointer;
    border: var(--border-width-md) solid transparent;
    transition: background var(--transition-duration-color), border var(--transition-duration-color);
}

    .filter-pill input[type="checkbox"] {
        accent-color: var(--primary-teal);
        margin-right: var(--space-0_3-rem);
    }

        .filter-pill:hover, .filter-pill input[type="checkbox"]:checked + span {
            background: var(--primary-teal);
            color: var(--primary-white);
            border: var(--border-width-md) solid var(--primary-teal);
        }

.filter-search {
    width: 100%;
    padding: var(--space-search-padding-y) var(--space-search-padding-x);
    border-radius: var(--radius-md);
    border: var(--border-width-sm) solid var( --secondary-gray-light);
    font-size: var(--font-size-base);
    background: var(--primary-white);
    font-weight: var(--font-weight-normal);
    margin-bottom: var(--space-unit-sm);
}

    .filter-search input[type="text"] {
        padding: var(--space-0_7-rem) var(--space-1_2-rem);
        border-radius: var(--radius-xxl);
        border: var(--border-width-md) solid var(--secondary-gray-light);
        font-size: var(--font-size-base);
        min-width: var(--sidebar-min-width);
        background: var(--primary-white);
        color: var(--tertiary-navy);
        outline: none;
        transition: border var(--transition-duration-color);
    }

        .filter-search input[type="text"]:focus {
            border: var(--border-width-md) solid var(--primary-teal);
        }

@media (max-width: 900px) {
    .advanced-filter {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-1_2-rem);
        padding: var(--space-1_2-rem) 1vw;
    }

    .filter-group {
        gap: var(--space-0_4-rem);
    }

    .filter-search input[type="text"] {
        min-width: var(--space-none);
        width: 100%;
    }
}

.advanced-filter-ui {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--space-unit-sm);
    max-width: var(--max-width-ui);
    margin: var(--space-none) auto;
}

.filter-group-row {
    display: flex;
    gap: var(--space-0_7-rem);
    flex-wrap: wrap;
    align-items: flex-start;
    width: 100%;
}

.filter-group-col {
    display: flex;
    flex-direction: column;
    gap: var(--space-0_7-rem);
    min-width: var(--filter-group-col-min-width);
    flex: 1 1 0;
    max-width: var(--auth-modal-min-width);
}

.filter-search-col {
    min-width: var(--sidebar-min-width);
    max-width: var(--sidebar-width);
}

.filter-group-label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--primary-teal);
    margin-bottom: var(--space-0_2-rem);
    letter-spacing: var(--letter-spacing-filter-label);
}

.filter-pills-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-0_6-rem);
}

.filter-search-input {
    padding: var(--space-0_7-rem) var(--space-1_2-rem);
    border-radius: var(--radius-xxl);
    border: var(--border-width-md) solid var(--secondary-gray-light);
    font-size: var(--font-size-base);
    background: var(--primary-white);
    color: var(--tertiary-navy);
    outline: none;
    transition: border var(--transition-duration-color);
    width: 100%;
}

    .filter-search-input:focus {
        border: var(--border-width-md) solid var(--primary-teal);
    }

@media (max-width: 900px) {
    .filter-group-row {
        flex-direction: column;
        gap: var(--space-1_2-rem);
    }

    .filter-group-col {
        max-width: 100%;
        min-width: var(--space-none);
    }

    .filter-pills-wrap {
        gap: var(--space-0_4-rem);
    }
}

@media (min-width: 1400px) {
    .advanced-filter-ui {
        max-width: var(--max-width-ui-lg);
        gap: var(--space-0_3-rem);
    }

    .filter-group-row {
        gap: var(--space-unit-sm);
    }
}

.sidebar-footer {
    margin-top: auto;
}
/* 01-07-2025 inline-V */
.assistant-fab-icon {
    width: var(--fab-icon-size);
    height: var(--fab-icon-size);
}

.auth-modal-absolute-content {
    position: absolute;
    width: 100%;
}

.fab-menu .fab-actions .fab-action {
    transition: opacity var(--transition-duration-slow), transform var(--transition-duration-slow);
    transition-delay: var(--fab-delay, 0ms);
    opacity: var(--space-none);
    transform: var(--transform-fab-action);
    pointer-events: none;
}

.fab-menu.open .fab-actions .fab-action {
    opacity: var(--z-index-default);
    transform: var(--transform-sidebar-open);
    pointer-events: auto;
}

.auth-modal-switch-link-mt {
    margin-top: var(--space-1_2-rem);
}

.password-strength-bar {
    width: var(--password-strength-width, 0%);
    transition: width var(--transition-duration-slow);
}

.auth-link-text-spaced {
    margin-left: var(--space-1_1-rem);
    margin-right: var(--space-1_1-rem);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    color: var(--primary-color, var(--text-link-active));
}

.header-cart-icon-wrap-pointer {
    cursor: pointer;
}

.sidebar-cart-link {
    font-weight: var(--font-weight-medium);
    color: var(--text-sidebar-cart);
    cursor: pointer;
}

.logo-stacked {
    display: flex;
    flex-direction: column;
}

.logo-align-left {
    align-items: flex-start;
    text-align: left;
}

.logo-align-center {
    align-items: center;
    text-align: center;
}

.logo-color-default .logo-main {
    color: var(--primary-color, var(--logo-main-color-default));
}

.logo-color-default .logo-sub {
    color: var(--text-logo-sub-default);
}

.logo-color-white .logo-main,
.logo-color-white .logo-sub {
    color: var(--primary-white);
}

.logo-main {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xxl);
    letter-spacing: var(--letter-spacing-logo-main);
}

.logo-sub {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    letter-spacing: var(--letter-spacing-logo-sub);
    margin-top: var(--logo-sub-margin-top);
}

.cart-industry-icon {
    background-image: var(--industry-icon-url);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.cart-count-item-pointer {
    cursor: pointer;
}

.cart-add-new-icon {
    margin-right: var(--radius-sm);
}

.cart-empty-icon-svg {
    font-size: var(--font-size-4-rem);
    opacity: var(--opacity-medium);
}

.cart-subscription-icon-wrap {
    display: flex;
    align-items: center;
    gap: var(--radius-sm);
}

.hero-btn {
    border-radius: var(--radius-xxl) !important;
    font-weight: var(--font-weight-bold) !important;
}

.hero-btn-margin {
    margin-right: var(--space-md) !important;
}

.section-header-no-margin {
    margin-bottom: var(--space-none);
}

.section-subtitle-no-margin {
    margin: var(--space-none);
}

.industries-toggle-icon {
    margin-right: var(--radius-sm);
}

.industry-card-body {
    background-image: var(--industry-icon-url);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: var(--industry-card-height);
    border-radius: var(--space-none) var(--space-none) var(--radius-card) var(--radius-card);
    /* background-color: var(--secondary-teal-extra-light); 	*/
    flex: 1 1 auto;
}

.module-btn {
    border-radius: var(--radius-xxl) !important;
    font-weight: var(--font-weight-bold) !important;
}

.plan-icon {
    font-size: var(--font-size-relative-xxl);
    margin-right: var(--space-xs);
    vertical-align: middle;
}

.plan-icon-leaf {
    color: var(--primary-teal-dark);
}

.plan-icon-medal-silver {
    color: var(--plan-icon-color-silver);
}

.plan-icon-medal-gold {
    color: var(--tag-color-gold);
}

.plan-icon-gem {
    color: var(--primary-blue);
}

.plan-quickview-icon {
    font-size: var(--font-size-relative-lg);
    vertical-align: middle;
    margin-right: var(--radius-sm);
}

.plan-quickview-icon-check {
    color: var(--primary-teal);
}

.plan-quickview-icon-cancel {
    color: var(--text-error-light);
}

.feature-comparison-title {
    text-align: center;
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--font-size-xxxl);
}

.feature-category-icon {
    margin-right: var(--space-xs);
    color: var(--text-feature-category);
}

    .feature-category-icon.open {
        color: var(--primary-white);
    }

.feature-category-body {
    display: none;
}

    .feature-category-body.open {
        display: block;
    }

.feature-comparison-center {
    text-align: center;
}

.feature-comparison-icon {
    font-size: var(--font-size-base);
    vertical-align: middle;
}

.feature-comparison-icon-check {
    color: var(--primary-teal);
}

.feature-comparison-icon-cancel {
    color: var(--text-error-light);
}

.pricing-card-btn {
    border-radius: var(--radius-card) !important;
    font-weight: var(--font-weight-bold) !important;
}
/* Sidebar positioning and transitions */
.sidebar-advanced-filter.mobile {
    position: fixed;
    top: var(--space-none);
    left: var(--space-none);
    z-index: var(--z-index-sidebar-advanced);
    height: 100%;
    transition: transform var(--transition-duration-slow) var(--transition-easing-cubic);
    box-shadow: none;
    transform: var(--transform-sidebar-closed);
}

    .sidebar-advanced-filter.mobile.open {
        transform: var(--transform-sidebar-open);
        box-shadow: var(--shadow-sidebar-open);
    }

.sidebar-advanced-filter.desktop {
    align-self: stretch;
    transition: transform var(--transition-duration-slow) var(--transition-easing-cubic);
    transform: var(--transform-sidebar-closed);
    box-shadow: none;
}

    .sidebar-advanced-filter.desktop.open {
        transform: var(--transform-sidebar-open);
        box-shadow: var(--shadow-sidebar-open);
    }

/* Header icon */
.sidebar-header-icon {
    margin-right: var(--space-xs);
}

/* Industry image thumb */
.industry-image-thumb {
    display: inline-block;
    width: var(--industry-image-thumb-size-sm);
    height: var(--industry-image-thumb-size-sm);
    margin-right: var(--space-filter-group-padding);
    vertical-align: middle;
    background-image: var(--industry-thumb-url);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: var(--radius-md);
    background-color: var(--background-feature-category);
}

/* Clear filters button */
.clear-filters-btn {
    color: var(--text-error-alt);
    font-weight: var(--font-weight-semibold);
    background: none;
    border: none;
    padding: var(--space-none);
    cursor: pointer;
}

.section-header-margin-top {
    margin-top: var(--space-unit-xl);
}

.sidebar-toggle-icon {
    margin-right: var(--radius-sm);
    font-size: var(--font-size-relative-xl);
    vertical-align: middle;
}

.suite-card-gap {
    width: var(--space-md);
    display: inline-block;
}



/* Admin Side Styles */

/* === Sidebar === */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--background-secondary);
    border-right: var(--border-width-sm) solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: width var(--transition-duration-slow) var(--transition-easing-ease);
    /* height: 100vh;
  min-height: 100vh; */
}

    .sidebar.collapsed {
        width: var(--sidebar-width-collapsed);
    }

        .sidebar.collapsed .sidebar-header h1,
        .sidebar.collapsed .sidebar-nav a span,
        .sidebar.collapsed .sidebar-footer .toggle-text {
            display: none;
        }

.sidebar-header {
    padding: var(--space-0_7-rem);
    border-bottom: var(--border-width-sm) solid var(--border-color);
    flex-shrink: 0;
}

    .sidebar-header h1 {
        margin: var(--space-none);
        font-size: var(--space-lg);
        color: var(--text-accent-hover);
        font-weight: var(--font-weight-bold);
    }

.sidebar-nav {
    flex-grow: 1;
    padding: var(--space-md) var(--space-sm);
    display: flex;
    flex-direction: column;
}

    .sidebar-nav a {
        display: flex;
        align-items: center;
        padding: var(--space-unit-sm);
        margin-bottom: var(--space-xxs);
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: var(--radius-md);
        font-weight: var(--font-weight-medium);
        transition: background-color var(--transition-duration-base), color var(--transition-duration-base);
        white-space: nowrap;
        font-size: var(--space-md);
    }

        .sidebar-nav a:hover {
            background-color: var(--background-primary);
            color: var(--text-primary);
        }

        .sidebar-nav a.active {
            background-color: var(--primary-teal);
            color: var(--text-white);
            font-weight: var(--font-weight-semibold);
        }

        .sidebar-nav a svg {
            flex-shrink: 0;
            font-size: var(--font-size-icon);
            margin-right: var(--space-md);
            min-width: var(--font-size-icon);
            min-height: var(--font-size-icon);
            max-width: var(--font-size-icon);
            max-height: var(--font-size-icon);
            display: inline-block;
        }

        .sidebar-nav a span {
            display: inline;
            opacity: 1;
            margin-left: var(--space-none);
        }

/* Hierarchical Navigation Styles */
.nav-parent-item {
    display: flex;
    align-items: center;
    padding: var(--space-unit-sm);
    margin-bottom: var(--space-xxs);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-base), color var(--transition-duration-base);
    white-space: nowrap;
    font-size: var(--space-md);
    cursor: pointer;
    position: relative;
}

    .nav-parent-item:hover,
    .nav-parent-item:focus {
        background-color: var(--background-primary);
        color: var(--text-primary);
        outline: none;
    }

    .nav-parent-item:focus {
        box-shadow: 0 0 0 2px var(--primary-teal);
        border-radius: var(--radius-md);
    }

    .nav-parent-item.collapsed {
        justify-content: center;
        margin-bottom: var(--space-xs);
    }

    .nav-parent-item svg:first-child {
        flex-shrink: 0;
        font-size: var(--font-size-icon);
        margin-right: var(--space-md);
        min-width: var(--font-size-icon);
        min-height: var(--font-size-icon);
        max-width: var(--font-size-icon);
        max-height: var(--font-size-icon);
        display: inline-block;
    }

    .nav-parent-item .expand-icon {
        margin-left: auto;
        display: flex;
        align-items: center;
        font-size: var(--font-size-sm);
        transition: transform var(--transition-duration-base) var(--transition-easing-ease);
    }

    .nav-parent-item .expand-icon svg {
        transition: transform var(--transition-duration-base) var(--transition-easing-ease);
    }

.nav-submenu {
    margin-left: var(--space-lg);
    border-left: 2px solid var(--border-color);
    padding-left: var(--space-sm);
    margin-bottom: var(--space-xs);
    padding-top: var(--space-xs);
    padding-bottom: var(--space-xs);
    animation: slideDown var(--transition-duration-base) var(--transition-easing-ease);
    overflow: hidden;
}

@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        max-height: 500px;
        opacity: 1;
        padding-top: var(--space-xs);
        padding-bottom: var(--space-xs);
    }
}

.nav-child-item {
    display: flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    margin-bottom: var(--space-xxs);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-sm);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-base), color var(--transition-duration-base);
    white-space: nowrap;
    font-size: var(--font-size-sm);
    position: relative;
    background-color: transparent;
}

    .nav-child-item:hover,
    .nav-child-item:focus {
        background-color: var(--background-primary);
        color: var(--text-primary);
        outline: none;
    }

    .nav-child-item:focus {
        box-shadow: 0 0 0 2px var(--primary-teal);
        border-radius: var(--radius-sm);
    }

    .nav-child-item.active {
        background-color: var(--primary-teal);
        color: var(--primary-white);
        font-weight: var(--font-weight-semibold);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transform: translateX(2px);
    }

    .nav-child-item.active .child-indicator {
        opacity: 1;
        color: var(--primary-white);
    }

    .nav-child-item .child-indicator {
        flex-shrink: 0;
        font-size: var(--font-size-xs);
        margin-right: var(--space-xs);
        opacity: 0.7;
        color: var(--primary-teal);
    }

    .nav-child-item svg:not(.child-indicator) {
        flex-shrink: 0;
        font-size: var(--font-size-sm);
        margin-right: var(--space-sm);
        min-width: var(--font-size-sm);
        min-height: var(--font-size-sm);
        max-width: var(--font-size-sm);
        max-height: var(--font-size-sm);
        display: inline-block;
    }

    .nav-child-item span {
        display: inline;
        opacity: 1;
        margin-left: var(--space-none);
    }

.sidebar.collapsed .sidebar-nav a {
    justify-content: center;
    margin-bottom: var(--space-xs);
}

    .sidebar.collapsed .sidebar-nav a svg {
        margin-right: var(--space-none);
    }

.sidebar.collapsed .nav-parent-item svg:first-child {
    margin-right: var(--space-none);
}

.sidebar.collapsed .nav-submenu {
    display: none;
}

.sidebar.collapsed .nav-parent-item span,
.sidebar.collapsed .nav-child-item span {
    display: none;
}

.sidebar.collapsed .sidebar-header {
    padding: var(--space-md) var(--space-none);
    display: flex;
    justify-content: center;
    align-items: center;
}

.sidebar.collapsed .sidebar-footer {
    padding: var(--space-md) var(--space-none);
    display: flex;
    justify-content: center;
    align-items: center;
}

.sidebar.collapsed .sidebar-toggle {
    width: 44px;
    height: 44px;
}

.sidebar-footer {
    padding: var(--space-lg) var(--space-none);
    border-top: var(--border-width-sm) solid var(--border-color);
    display: flex;
    justify-content: center;
    align-items: flex-end;
    min-height: 80px;
}

.sidebar-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: var(--background-secondary);
    border: var(--border-width-md) solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    color: var(--text-secondary);
    font-size: var(--font-size-icon);
    margin: var(--space-none) auto;
}

/* === Topbar === */
.mui-topbar-appbar {
    background: var(--primary-white) !important;
    color: var(--topbar-icon) !important;
    border-bottom: var(--border-width-sm) solid var(--border-color);
    z-index: 100;
    width: 100%;
    transition: width 0.3s, margin-left 0.3s;
    height: 64px;
    justify-content: center;
    position: sticky;
    top: 0;
    left: 0;
}

.mui-topbar-toolbar {
    min-height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--space-0_7-rem);
    position: relative;
}

.mui-topbar-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.mui-topbar-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    pointer-events: none;
}

.mui-topbar-searchbox {
    min-width: 320px !important;
    max-width: 480px !important;
    background: var(--primary-white);
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    pointer-events: auto;
}

.mui-topbar-actions {
    display: flex;
    align-items: center;
    gap: 1.2rem;
}

.mui-topbar-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 0.5rem;
    border: 2px solid var(--color-primary-teal);
    box-shadow: 0 1px 4px rgba(0,0,0,0.06);
}

.main-layout {
    height: 100vh;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1 1 0;
    overflow-y: auto;
    min-height: 0;
    padding: var(--space-unit-lg);
    background: var(--primary-white);
}


/* Industry Cards Horizontal Scroll Styles */
.industry-cards-grid {
    display: flex;
    flex-direction: row;
    gap: var(--space-xl);
    margin-top: var(--space-xxl);
    margin-bottom: var(--space-xxl);
    padding-left: var(--space-xxl);
    padding-right: var(--space-xxl);
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
}

.industry-card {
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    box-shadow: 0 2px 16px 0 rgba(16, 42, 67, 0.08);
    display: flex;
    flex-direction: row;
    align-items: stretch;
    overflow: hidden;
    transition: box-shadow var(--transition-duration-base);
    min-width: 320px;
    max-width: 360px;
    min-height: 220px;
    height: 220px;
    flex: 0 0 auto;
}

    .industry-card:hover {
        box-shadow: 0 6px 32px 0 rgba(16, 42, 67, 0.16);
    }

.industry-card-content {
    flex: 1 1 auto;
    padding: var(--space-xl) var(--space-xl) var(--space-xl) var(--space-lg);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.industry-card-title {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-primary);
    background: var(--hcl-gradient-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.industry-card-desc {
    font-size: 0.95rem;
    color: var(--text-secondary);
    margin: 0;
}

.industry-card-img-wrap {
    flex: 0 0 220px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-accent-light);
    min-height: 100%;
    min-width: 180px;
    width: 180px; /* Explicit width for reference */
    height: 100%; /* Will match parent height, typically 220px */
}

.industry-card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(.4,1.3,.5,1);
}

.industry-card:hover .industry-card-img {
    transform: scale(1.1);
}

@media (max-width: 900px) {
    .industry-cards-grid {
        gap: var(--space-lg);
        padding-left: var(--space-lg);
        padding-right: var(--space-lg);
    }

    .industry-card {
        min-width: 260px;
        max-width: 320px;
        min-height: 180px;
        height: 180px;
    }
}

@media (max-width: 600px) {
    .industry-cards-grid {
        gap: var(--space-md);
        padding-left: var(--space-md);
        padding-right: var(--space-md);
    }

    .industry-card {
        min-width: 220px;
        max-width: 260px;
        min-height: 140px;
        height: 140px;
    }
}

/* Hide default scrollbar */
.industry-cards-grid {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
}

    .industry-cards-grid::-webkit-scrollbar {
        display: none;
    }

/* Custom progress bar for horizontal scroll */
.industry-scroll-progress {
    width: 100%;
    height: 6px;
    background: var(--background-accent-light);
    border-radius: var(--radius-full);
    margin-top: var(--space-md);
    position: relative;
    overflow: hidden;
}

.industry-scroll-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-teal), var(--primary-blue));
    border-radius: var(--radius-full);
    transition: width 0.2s;
}

/* Wider cards */
.industry-card {
    min-width: 420px;
    max-width: 480px;
}

@media (max-width: 900px) {
    .industry-card {
        min-width: 320px;
        max-width: 380px;
    }
}

@media (max-width: 600px) {
    .industry-card {
        min-width: 220px;
        max-width: 260px;
    }
}

/* === Styles for Top Navigation Link === */
.top-nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    transition: background-color 0.2s, color 0.2s;
    background-color: transparent;
    color: var(--text-primary);
    text-decoration: none;
    white-space: nowrap;
}

    /* Hover state for non-active links */
    .top-nav-link:hover {
        background-color: var(--background-hover-light);
        color: var(--text-accent);
    }

    /* Active state for the link */
    .top-nav-link.active {
        background-color: var(--primary-teal);
        color: var(--primary-white);
    }

        /* Ensure icon color also changes on active state */
        .top-nav-link.active .MuiSvgIcon-root {
            color: var(--primary-white);
        }
