import React from 'react';
import DynamicIcon from '../common/DynamicIcon';
// import './ModuleCard.css';
import Button from '@mui/material/Button';

const ModuleCard = ({ module, handleAddToCart }) => {
    const { name, category, description, icon } = module;
    return (
        <div className="module-card">
            {module.tags && module.tags.length > 0 && (
                <div className={`module-tag-badge tag-${module.tags[0].toLowerCase().replace(/\s+/g, '-')}`}>{module.tags[0]}</div>
            )}
            <div className="card-header">
                <div className="card-icon-container">
                    <DynamicIcon name={icon} />
                </div>
                <div className="card-title-container">
                    <h4 className="card-title">{name}</h4>
                    <p className="card-category">{category}</p>
                </div>
            </div>
            <p className="card-description">{description}</p>
            <div className="card-footer">
                <div className="card-actions">
                    <Button variant="contained" color="primary" size="small" className="module-btn">
                        Request For Demo
                    </Button>
                    <Button variant="outlined" color="primary" size="small" onClick={() => handleAddToCart(module)} className="module-btn">
                        Add to Cart
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ModuleCard; 