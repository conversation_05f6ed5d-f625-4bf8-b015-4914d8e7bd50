import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box, Paper, Typography, Button, Grid, TextField, InputAdornment, IconButton,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination,
  Chip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
  Tooltip, Menu, MenuItem, ListItemIcon, ListItemText, Tabs, Tab, CircularProgress,
  Card, CardContent, Divider, Alert
} from '@mui/material';
import {
  Add, Search, FilterList, MoreVert, Edit, Delete, Visibility, FileDownload,
  Refresh, Sort, ArrowUpward, ArrowDownward, CheckCircle, Cancel, Warning,
  Assessment, Upload, GetApp, ViewList
} from '@mui/icons-material';
import { AdminComponents } from '../../../styles/theme';
import PurchaseOrderForm from './PurchaseOrderForm';
import PurchaseOrderCharts from './PurchaseOrderCharts';
import { calculateDerivedValues } from '../../../models/PurchaseOrderModel';
// import { exportToExcel, exportToCSV, exportToPDF, importFromFile, validateImportData } from '../../../utils/purchaseOrderExport';
import purchaseOrderTranslations from '../../../locales/en/purchaseOrder.json';

// Mock data for demonstration
const mockPurchaseOrders = [
  {
    id: 'po-001',
    owner: 'John Smith',
    parentCompanyName: 'Acme Corporation',
    accountName: 'Acme Global Services',
    domainVertical: 'Manufacturing',
    region: 'North America',
    revenueType: 'License',
    poNumber: 'PO-2025-001',
    poCurrency: 'USD',
    poValue: 100000,
    conversionRate: 83.5,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Monthly',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Software',
    productName: 'HCL Commerce Suite',
    projectDescription: 'Enterprise e-commerce platform implementation',
    status: 'Approved',
    createdBy: 'Jane Doe',
    createdDate: '2024-03-15T10:30:00Z',
    lastModifiedBy: 'Jane Doe',
    lastModifiedDate: '2024-03-15T10:30:00Z',
    projectedApr25: 8333.33,
    projectedMay25: 8333.33,
    projectedJun25: 8333.33,
    projectedJul25: 8333.33,
    projectedAug25: 8333.33,
    projectedSep25: 8333.33,
    projectedOct25: 8333.33,
    projectedNov25: 8333.33,
    projectedDec25: 8333.33,
    projectedJan26: 8333.33,
    projectedFeb26: 8333.33,
    projectedMar26: 8333.37,
    actualApr25: 8333.33,
    actualMay25: 8333.33,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  },
  {
    id: 'po-002',
    owner: 'Sarah Johnson',
    parentCompanyName: 'TechGlobal Inc.',
    accountName: 'TechGlobal Solutions',
    domainVertical: 'Technology',
    region: 'Europe',
    revenueType: 'Services',
    poNumber: 'PO-2025-002',
    poCurrency: 'EUR',
    poValue: 75000,
    conversionRate: 90.2,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Quarterly',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Professional Services',
    productName: 'HCL Digital Experience',
    projectDescription: 'Digital transformation consulting services',
    status: 'Pending',
    createdBy: 'Mike Wilson',
    createdDate: '2024-03-10T14:45:00Z',
    lastModifiedBy: 'Mike Wilson',
    lastModifiedDate: '2024-03-10T14:45:00Z',
    projectedApr25: 16875,
    projectedMay25: 16875,
    projectedJun25: 16875,
    projectedJul25: 16875,
    projectedAug25: 16875,
    projectedSep25: 16875,
    projectedOct25: 16875,
    projectedNov25: 16875,
    projectedDec25: 16875,
    projectedJan26: 16875,
    projectedFeb26: 16875,
    projectedMar26: 16875,
    actualApr25: 16875,
    actualMay25: 0,
    actualJun25: 0,
    actualJul25: 0,
    actualAug25: 0,
    actualSep25: 0,
    actualOct25: 0,
    actualNov25: 0,
    actualDec25: 0,
    actualJan26: 0,
    actualFeb26: 0,
    actualMar26: 0
  }
].map(po => calculateDerivedValues(po));

const PurchaseOrderManagement = () => {
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('lastModifiedDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedPO, setSelectedPO] = useState(null);
  const [formMode, setFormMode] = useState(null); // null, 'create', 'edit', 'view'
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [activeTab, setActiveTab] = useState(0); // 0 = List, 1 = Analytics
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);
  const [exportMenuAnchor, setExportMenuAnchor] = useState(null);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importErrors, setImportErrors] = useState({ errors: [], warnings: [] });
  const [importPreview, setImportPreview] = useState([]);
  const [filters, setFilters] = useState({
    status: null,
    region: null,
    revenueType: null
  });
  const fileInputRef = useRef(null);

  const t = (key) => {
    const keys = key.split('.');
    let value = purchaseOrderTranslations;
    for (const k of keys) {
      value = value?.[k];
    }
    return value || key;
  };

  // Fetch purchase orders (mock implementation)
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setPurchaseOrders(mockPurchaseOrders);
      setLoading(false);
    }, 1000);
  }, []);

  // Handle search and filtering
  const filteredPurchaseOrders = useCallback(() => {
    return purchaseOrders.filter(po => {
      // Search term filtering
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = 
        !searchTerm ||
        po.poNumber.toLowerCase().includes(searchLower) ||
        po.accountName.toLowerCase().includes(searchLower) ||
        po.parentCompanyName.toLowerCase().includes(searchLower) ||
        po.productName.toLowerCase().includes(searchLower) ||
        po.owner.toLowerCase().includes(searchLower);
      
      // Status filtering
      const matchesStatus = !filters.status || po.status === filters.status;
      
      // Region filtering
      const matchesRegion = !filters.region || po.region === filters.region;
      
      // Revenue Type filtering
      const matchesRevenueType = !filters.revenueType || po.revenueType === filters.revenueType;
      
      return matchesSearch && matchesStatus && matchesRegion && matchesRevenueType;
    }).sort((a, b) => {
      // Sorting
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (typeof aValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortDirection === 'asc' 
          ? aValue - bValue
          : bValue - aValue;
      }
    });
  }, [purchaseOrders, searchTerm, sortBy, sortDirection, filters]);

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleActionMenuOpen = (event, po) => {
    setSelectedPO(po);
    setActionMenuAnchor(event.currentTarget);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
  };

  const handleFilterMenuOpen = (event) => {
    setFilterMenuAnchor(event.currentTarget);
  };

  const handleFilterMenuClose = () => {
    setFilterMenuAnchor(null);
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
    setFilterMenuAnchor(null);
  };

  const handleCreatePO = () => {
    setSelectedPO(null);
    setFormMode('create');
  };

  const handleEditPO = (po) => {
    setSelectedPO(po);
    setFormMode('edit');
    handleActionMenuClose();
  };

  const handleViewPO = (po) => {
    setSelectedPO(po);
    setFormMode('view');
    handleActionMenuClose();
  };

  const handleDeleteClick = (po) => {
    setSelectedPO(po);
    setDeleteDialogOpen(true);
    handleActionMenuClose();
  };

  const handleDeleteConfirm = () => {
    // Delete implementation would go here
    setPurchaseOrders(prev => prev.filter(po => po.id !== selectedPO.id));
    setDeleteDialogOpen(false);
    setSelectedPO(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedPO(null);
  };

  const handleFormCancel = () => {
    setFormMode(null);
    setSelectedPO(null);
  };

  const handleFormSave = (formData) => {
    if (formMode === 'create') {
      // Add new PO with generated ID
      const newPO = {
        ...formData,
        id: `po-${Date.now()}`,
        createdBy: 'Current User', // Should come from auth context
        createdDate: new Date().toISOString(),
      };
      setPurchaseOrders(prev => [...prev, newPO]);
    } else if (formMode === 'edit') {
      // Update existing PO
      setPurchaseOrders(prev => 
        prev.map(po => po.id === formData.id ? formData : po)
      );
    }
    
    setFormMode(null);
    setSelectedPO(null);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleStatusTabChange = (event, newValue) => {
    // Set filters based on status tab
    switch(newValue) {
      case 0: // All
        setFilters(prev => ({ ...prev, status: null }));
        break;
      case 1: // Draft
        setFilters(prev => ({ ...prev, status: 'Draft' }));
        break;
      case 2: // Pending
        setFilters(prev => ({ ...prev, status: 'Pending' }));
        break;
      case 3: // Approved
        setFilters(prev => ({ ...prev, status: 'Approved' }));
        break;
      case 4: // Completed
        setFilters(prev => ({ ...prev, status: 'Completed' }));
        break;
      default:
        setFilters(prev => ({ ...prev, status: null }));
    }
  };

  const handleExportMenuOpen = (event) => {
    setExportMenuAnchor(event.currentTarget);
  };

  const handleExportMenuClose = () => {
    setExportMenuAnchor(null);
  };

  const handleExport = async (format) => {
    // Temporarily disabled - will be implemented after dependencies are properly installed
    console.log(`Export to ${format} requested`);
    handleExportMenuClose();
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event) => {
    // Temporarily disabled - will be implemented after dependencies are properly installed
    console.log('File import requested');
    event.target.value = '';
  };

  const handleImportConfirm = async () => {
    // Temporarily disabled
    console.log('Import confirmation requested');
    setImportDialogOpen(false);
  };

  const handleImportCancel = () => {
    setImportDialogOpen(false);
    setImportFile(null);
    setImportPreview([]);
    setImportErrors({ errors: [], warnings: [] });
  };

  const getStatusChipColor = (status) => {
    switch(status) {
      case 'Draft': return 'default';
      case 'Pending': return 'warning';
      case 'Approved': return 'success';
      case 'Rejected': return 'error';
      case 'Completed': return 'primary';
      case 'Cancelled': return 'error';
      default: return 'default';
    }
  };

  // If form mode is active, show the form
  if (formMode) {
    return (
      <PurchaseOrderForm
        purchaseOrder={selectedPO}
        onSave={handleFormSave}
        onCancel={handleFormCancel}
        mode={formMode}
      />
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" sx={{ mb: 3 }}>
        {t('title')}
      </Typography>

      {/* Main Tabs - List vs Analytics */}
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}
      >
        <Tab label="Purchase Orders List" icon={<ViewList />} />
        <Tab label="Analytics Dashboard" icon={<Assessment />} />
      </Tabs>

      {/* Analytics View */}
      {activeTab === 1 && (
        <PurchaseOrderCharts purchaseOrders={purchaseOrders} />
      )}

      {/* List View */}
      {activeTab === 0 && (
        <>
          {/* Status Filter Tabs */}
          <Tabs
            value={filters.status ? ['Draft', 'Pending', 'Approved', 'Completed'].indexOf(filters.status) + 1 : 0}
            onChange={handleStatusTabChange}
            sx={{ mb: 3 }}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="All" />
            <Tab label="Draft" />
            <Tab label="Pending" />
            <Tab label="Approved" />
            <Tab label="Completed" />
          </Tabs>

      {/* Action Bar */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, flexWrap: 'wrap', gap: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <TextField
            placeholder={t('buttons.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
          
          <Button
            variant="outlined"
            startIcon={<FilterList />}
            onClick={handleFilterMenuOpen}
            size="small"
          >
            {t('buttons.filter')}
          </Button>
          
          <Menu
            anchorEl={filterMenuAnchor}
            open={Boolean(filterMenuAnchor)}
            onClose={handleFilterMenuClose}
          >
            <MenuItem onClick={() => handleFilterChange('region', null)}>
              All Regions
            </MenuItem>
            <MenuItem onClick={() => handleFilterChange('region', 'North America')}>
              North America
            </MenuItem>
            <MenuItem onClick={() => handleFilterChange('region', 'Europe')}>
              Europe
            </MenuItem>
            <MenuItem onClick={() => handleFilterChange('region', 'Asia Pacific')}>
              Asia Pacific
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => handleFilterChange('revenueType', null)}>
              All Revenue Types
            </MenuItem>
            <MenuItem onClick={() => handleFilterChange('revenueType', 'License')}>
              License
            </MenuItem>
            <MenuItem onClick={() => handleFilterChange('revenueType', 'Services')}>
              Services
            </MenuItem>
            <MenuItem onClick={() => handleFilterChange('revenueType', 'Support')}>
              Support
            </MenuItem>
          </Menu>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<Upload />}
            onClick={handleImportClick}
            size="small"
          >
            Import
          </Button>

          <Button
            variant="outlined"
            startIcon={<GetApp />}
            onClick={handleExportMenuOpen}
            size="small"
          >
            Export
          </Button>

          <Menu
            anchorEl={exportMenuAnchor}
            open={Boolean(exportMenuAnchor)}
            onClose={handleExportMenuClose}
          >
            <MenuItem onClick={() => handleExport('excel')}>
              <ListItemIcon>
                <FileDownload fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export to Excel</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleExport('csv')}>
              <ListItemIcon>
                <FileDownload fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export to CSV</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleExport('pdf')}>
              <ListItemIcon>
                <FileDownload fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export to PDF</ListItemText>
            </MenuItem>
          </Menu>

          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreatePO}
          >
            {t('buttons.add')}
          </Button>
        </Box>
      </Box>

      {/* Active Filters */}
      {(filters.region || filters.revenueType) && (
        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
          <Typography variant="body2" sx={{ mr: 1 }}>Active Filters:</Typography>
          {filters.region && (
            <Chip 
              label={`Region: ${filters.region}`} 
              onDelete={() => handleFilterChange('region', null)}
              size="small"
            />
          )}
          {filters.revenueType && (
            <Chip 
              label={`Revenue Type: ${filters.revenueType}`} 
              onDelete={() => handleFilterChange('revenueType', null)}
              size="small"
            />
          )}
          <Button 
            variant="text" 
            size="small"
            onClick={() => setFilters({ status: filters.status, region: null, revenueType: null })}
          >
            Clear Filters
          </Button>
        </Box>
      )}

      {/* Purchase Orders Table */}
      <TableContainer component={Paper} sx={{ mb: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : filteredPurchaseOrders().length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="body1">{t('messages.noData')}</Typography>
          </Box>
        ) : (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <Box 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSort('poNumber')}
                  >
                    {t('labels.poNumber')}
                    {sortBy === 'poNumber' && (
                      sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSort('accountName')}
                  >
                    {t('labels.accountName')}
                    {sortBy === 'accountName' && (
                      sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSort('productName')}
                  >
                    {t('labels.productName')}
                    {sortBy === 'productName' && (
                      sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSort('poValue')}
                  >
                    {t('labels.poValue')}
                    {sortBy === 'poValue' && (
                      sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSort('grandTotalINR')}
                  >
                    {t('labels.grandTotalINR')}
                    {sortBy === 'grandTotalINR' && (
                      sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                    onClick={() => handleSort('status')}
                  >
                    Status
                    {sortBy === 'status' && (
                      sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredPurchaseOrders()
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((po) => (
                  <TableRow key={po.id} hover>
                    <TableCell>{po.poNumber}</TableCell>
                    <TableCell>{po.accountName}</TableCell>
                    <TableCell>{po.productName}</TableCell>
                    <TableCell>
                      {po.poValue.toLocaleString('en-IN', { 
                        style: 'currency', 
                        currency: po.poCurrency 
                      })}
                    </TableCell>
                    <TableCell>
                      {po.grandTotalINR.toLocaleString('en-IN', { 
                        style: 'currency', 
                        currency: 'INR' 
                      })}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={po.status} 
                        color={getStatusChipColor(po.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={(e) => handleActionMenuOpen(e, po)}>
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        )}
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredPurchaseOrders().length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        <MenuItem onClick={() => handleViewPO(selectedPO)}>
          <ListItemIcon>
            <Visibility fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('buttons.view')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleEditPO(selectedPO)}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('buttons.edit')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleDeleteClick(selectedPO)}>
          <ListItemIcon>
            <Delete fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('buttons.delete')}</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('messages.confirmDelete')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Import Dialog */}
      <Dialog
        open={importDialogOpen}
        onClose={handleImportCancel}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Import Purchase Orders</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Preview of data to be imported from {importFile?.name}
          </DialogContentText>

          {importErrors.errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              <Typography variant="subtitle2">Errors:</Typography>
              <ul>
                {importErrors.errors.map((error, index) => (
                  <li key={`error-${index}`}>{error}</li>
                ))}
              </ul>
            </Alert>
          )}

          {importErrors.warnings.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="subtitle2">Warnings:</Typography>
              <ul>
                {importErrors.warnings.map((warning, index) => (
                  <li key={`warning-${index}`}>{warning}</li>
                ))}
              </ul>
            </Alert>
          )}

          {importPreview.length > 0 && (
            <TableContainer component={Paper} sx={{ mb: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>PO Number</TableCell>
                    <TableCell>Account Name</TableCell>
                    <TableCell>Product</TableCell>
                    <TableCell>PO Value</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {importPreview.map((po, index) => (
                    <TableRow key={`preview-${index}`}>
                      <TableCell>{po.poNumber}</TableCell>
                      <TableCell>{po.accountName}</TableCell>
                      <TableCell>{po.productName}</TableCell>
                      <TableCell>{po.poValue}</TableCell>
                      <TableCell>{po.status}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          <Typography variant="body2" color="text.secondary">
            {importPreview.length > 0
              ? `Showing ${importPreview.length} of ${importFile ? 'the imported records' : '0 records'}.`
              : 'No preview available.'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleImportCancel}>Cancel</Button>
          <Button
            onClick={handleImportConfirm}
            color="primary"
            disabled={importErrors.errors.length > 0 || importPreview.length === 0}
          >
            Import Data
          </Button>
        </DialogActions>
      </Dialog>

        </>
      )}

      {/* Hidden file input for import */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept=".xlsx,.xls,.csv"
        onChange={handleFileSelect}
      />
    </Box>
  );
};

export default PurchaseOrderManagement;
