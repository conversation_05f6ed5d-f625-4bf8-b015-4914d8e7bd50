import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, Avatar, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, LocalShipping, ShowChart, PieChart, DonutLarge,
    Settings, MoreVert, ArrowUpward, ArrowDownward, Assessment, TrendingUp, Save, Close
} from '@mui/icons-material';
import { calculateDerivedValues } from '../../../models/PurchaseOrderModel';
import { theme, AdminComponents } from '../../../styles/theme';
import purchaseOrderTranslations from '../../../locales/en/purchaseOrder.json';

// Translation function
const t = (key) => {
    const keys = key.split('.');
    let value = purchaseOrderTranslations;
    for (const k of keys) {
        value = value?.[k];
    }
    return value || key;
};

// --- MOCK DATA ---
const initialPurchaseOrders = [
  {
    id: 'po-001',
    owner: 'John Smith',
    parentCompanyName: 'Acme Corporation',
    accountName: 'Acme Global Services',
    domainVertical: 'Manufacturing',
    region: 'North America',
    revenueType: 'License',
    poNumber: 'PO-2025-001',
    poCurrency: 'USD',
    poValue: 100000,
    conversionRate: 83.5,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Monthly',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Software',
    productName: 'HCL Commerce Suite',
    projectDescription: 'Enterprise e-commerce platform implementation',
    status: 'Approved',
    createdBy: 'Jane Doe',
    createdDate: '2024-03-15T10:30:00Z',
    lastModifiedBy: 'Jane Doe',
    lastModifiedDate: '2024-03-15T10:30:00Z',
    grandTotalINR: 8350000,
    grandTotalUSD: 100000,
  },
  {
    id: 'po-002',
    owner: 'Sarah Johnson',
    parentCompanyName: 'TechGlobal Inc.',
    accountName: 'TechGlobal Solutions',
    domainVertical: 'Technology',
    region: 'Europe',
    revenueType: 'Services',
    poNumber: 'PO-2025-002',
    poCurrency: 'EUR',
    poValue: 75000,
    conversionRate: 90.2,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Quarterly',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'New',
    productRevenueCategory: 'Professional Services',
    productName: 'HCL Digital Experience',
    projectDescription: 'Digital transformation consulting services',
    status: 'Pending',
    createdBy: 'Mike Wilson',
    createdDate: '2024-03-10T14:45:00Z',
    lastModifiedBy: 'Mike Wilson',
    lastModifiedDate: '2024-03-10T14:45:00Z',
    grandTotalINR: 6765000,
    grandTotalUSD: 81000,
  },
  {
    id: 'po-003',
    owner: 'David Chen',
    parentCompanyName: 'Global Retail Corp',
    accountName: 'Global Retail Solutions',
    domainVertical: 'Retail',
    region: 'Asia Pacific',
    revenueType: 'License',
    poNumber: 'PO-2025-003',
    poCurrency: 'INR',
    poValue: 5000000,
    conversionRate: 1,
    hclConversionRateUSD: 83.5,
    opportunityStage: 'Closed Won',
    frequencyOfRealization: 'Annually',
    startDate: '2025-04-01',
    endDate: '2026-03-31',
    renewalUpsell: 'Renewal',
    productRevenueCategory: 'Software',
    productName: 'HCL Commerce Cloud',
    projectDescription: 'Cloud-based retail platform upgrade',
    status: 'Draft',
    createdBy: 'Lisa Wang',
    createdDate: '2024-03-20T09:15:00Z',
    lastModifiedBy: 'Lisa Wang',
    lastModifiedDate: '2024-03-20T09:15:00Z',
    grandTotalINR: 5000000,
    grandTotalUSD: 59880,
  }
].map(po => calculateDerivedValues(po));

const ALL_COLUMNS = [
    { key: 'poNumber', label: 'PO Number', type: 'string' },
    { key: 'accountName', label: 'Account Name', type: 'string' },
    { key: 'productName', label: 'Product Name', type: 'string' },
    { key: 'poValue', label: 'PO Value', type: 'currency' },
    { key: 'grandTotalINR', label: 'Total (INR)', type: 'currency' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'owner', label: 'Owner', type: 'string' },
    { key: 'region', label: 'Region', type: 'string' },
    { key: 'revenueType', label: 'Revenue Type', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ purchaseOrder, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(purchaseOrder)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(purchaseOrder)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([purchaseOrder.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const PurchaseTable = ({ purchases, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < purchases.length} checked={purchases.length > 0 && selectedIds.length === purchases.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {purchases.map(p => (
                        <TableRow key={p.id} hover selected={selectedId === p.id} onClick={() => onRowClick(p)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(p.id)} onChange={() => onSelectOne(p.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{p[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons purchase={p} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const PurchaseCard = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(purchase.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{purchase.item}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{purchase.orderId}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Quantity:</strong> {purchase.quantity}</Typography>
            <Typography variant="body2"><strong>Price:</strong> {purchase.price}</Typography>
            <Typography variant="body2"><strong>PO Start:</strong> {purchase.poStartDate}</Typography>
            <Typography variant="body2"><strong>PO End:</strong> {purchase.poEndDate}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const PurchaseCompactCard = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(purchase.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{purchase.item}</Typography>
                <Typography variant="caption" color="text.secondary">{purchase.orderId}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{purchase.status}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const PurchaseListItem = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(purchase.id)} />
            <Box>
                <Typography fontWeight="bold">{purchase.item}</Typography>
                <Typography variant="body2" color="text.secondary">{purchase.orderId}</Typography>
            </Box>
            <Typography variant="body2">{purchase.quantity}</Typography>
            <Typography variant="body2">{purchase.status}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const PurchaseGraph = ({ purchase, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && purchase) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Quantity'],
                    datasets: [{
                        label: 'Order Quantity',
                        data: [purchase.quantity],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${purchase.item} - Quantity` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [purchase, chartType]);

    return (
        <>
            {purchase ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a purchase order to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const Purchase = () => {
    const [purchaseOrders, setPurchaseOrders] = useState(initialPurchaseOrders);
    const [selectedPurchase, setSelectedPurchase] = useState(null);
    const [sortColumn, setSortColumn] = useState('orderId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [viewMode, setViewMode] = useState('cards');
    const [isGraphVisible, setIsGraphVisible] = useState(true);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new purchase order', target: 'PO12345', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Approved purchase order', target: 'PO12346', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Finance', action: 'Marked as shipped', target: 'PO12346', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [chartType, setChartType] = useState('bar');
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [activeTab, setActiveTab] = useState(0); // 0 = Basic, 1 = Advanced
    // Calculate summary statistics
    const summaryStats = useMemo(() => {
        const totalPOs = purchaseOrders.length;
        const totalValue = purchaseOrders.reduce((sum, po) => sum + (po.grandTotalINR || 0), 0);
        const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

        return { totalPOs, totalValue, avgValue };
    }, [purchaseOrders]);
    const handleAddQuickFilter = (value) => {
        const statusValues = ['Completed', 'Shipped', 'Pending'];
        const field = statusValues.includes(value) ? 'status' : 'item';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    // Advanced Search Handlers
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    // Handle search and filtering
    const filteredPurchaseOrders = useCallback(() => {
        return purchaseOrders.filter(po => {
            // Search term filtering
            const searchLower = searchTerm.toLowerCase();
            const matchesSearch =
                !searchTerm ||
                po.poNumber.toLowerCase().includes(searchLower) ||
                po.accountName.toLowerCase().includes(searchLower) ||
                po.parentCompanyName.toLowerCase().includes(searchLower) ||
                po.productName.toLowerCase().includes(searchLower) ||
                po.owner.toLowerCase().includes(searchLower);

            // Status filtering
            const matchesStatus = !filter || filter === 'all' || po.status.toLowerCase() === filter;

            return matchesSearch && matchesStatus;
        }).sort((a, b) => {
            // Sorting
            const aValue = a[sortColumn] || a.poNumber;
            const bValue = b[sortColumn] || b.poNumber;

            if (typeof aValue === 'string') {
                return sortDirection === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            } else {
                return sortDirection === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            }
        });
    }, [purchaseOrders, searchTerm, sortColumn, sortDirection, filter]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (purchase) => setSelectedPurchase(purchase);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedPurchases.map(p => p.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const displayPurchase = useMemo(() => {
        const isSelectedVisible = processedPurchases.some(p => p.id === selectedPurchase?.id);
        if (isSelectedVisible) return selectedPurchase;
        return processedPurchases.length > 0 ? processedPurchases[0] : null;
    }, [processedPurchases, selectedPurchase]);

    // Get filtered data for display
    const displayData = filteredPurchaseOrders();

    // Event handlers
    const handleSort = (field) => {
        if (sortColumn === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortColumn(field);
            setSortDirection('asc');
        }
    };

    const handleView = (po) => {
        setSelectedPurchase(po);
    };

    const handleEdit = (po) => {
        setSelectedPurchase(po);
    };

    const handleDelete = (ids) => {
        setPurchaseOrders(prev => prev.filter(po => !ids.includes(po.id)));
    };

    const getStatusChipColor = (status) => {
        switch(status) {
            case 'Draft': return 'default';
            case 'Pending': return 'warning';
            case 'Approved': return 'success';
            case 'Rejected': return 'error';
            case 'Completed': return 'primary';
            case 'Cancelled': return 'error';
            default: return 'default';
        }
    };

    const formatCurrency = (value, currency = 'INR') => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    };

    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    // If Advanced tab is selected, show the comprehensive Purchase Order Management
    if (activeTab === 1) {
        return (
            <ThemeProvider theme={theme}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                    <Tabs value={activeTab} onChange={handleTabChange}>
                        <Tab label="Basic Purchase Orders" />
                        <Tab label="Advanced Purchase Order Management" />
                    </Tabs>
                </Box>
                <PurchaseOrderManagement />
            </ThemeProvider>
        );
    }

    // Unified Purchase Order Management Interface
    return (
        <ThemeProvider theme={theme}>
            <Box sx={{ p: 3 }}>
                <Typography variant="h4" component="h1" sx={{ mb: 3 }}>
                    {t('title')}
                </Typography>

                {/* Summary Cards Section */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                    <Grid item xs={12} sm={6} md={4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom variant="body2">
                                            Total Purchase Orders
                                        </Typography>
                                        <Typography variant="h4" component="div">
                                            {summaryStats.totalPOs}
                                        </Typography>
                                    </Box>
                                    <Assessment color="primary" sx={{ fontSize: 40 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom variant="body2">
                                            Total Value
                                        </Typography>
                                        <Typography variant="h5" component="div">
                                            {formatCurrency(summaryStats.totalValue)}
                                        </Typography>
                                    </Box>
                                    <TrendingUp color="success" sx={{ fontSize: 40 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom variant="body2">
                                            Average Value
                                        </Typography>
                                        <Typography variant="h5" component="div">
                                            {formatCurrency(summaryStats.avgValue)}
                                        </Typography>
                                    </Box>
                                    <BarChart color="info" sx={{ fontSize: 40 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>

                {/* Action Bar */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <TextField
                            placeholder="Search purchase orders..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            size="small"
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <Search />
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <ToggleButtonGroup
                            value={viewMode}
                            exclusive
                            onChange={(e, newMode) => newMode && setViewMode(newMode)}
                            size="small"
                        >
                            <ToggleButton value="table" title="Table View">
                                <ViewList />
                            </ToggleButton>
                            <ToggleButton value="cards" title="Card View">
                                <ViewModule />
                            </ToggleButton>
                            <ToggleButton value="compact" title="Compact View">
                                <GridView />
                            </ToggleButton>
                        </ToggleButtonGroup>
                    </Box>
                </Box>

                {/* Purchase Orders Table */}
                <TableContainer component={Paper} sx={{ mb: 3 }}>
                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : displayData.length === 0 ? (
                        <Box sx={{ p: 4, textAlign: 'center' }}>
                            <Typography variant="body1">No purchase orders found</Typography>
                        </Box>
                    ) : (
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>
                                        <Box
                                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                            onClick={() => handleSort('poNumber')}
                                        >
                                            PO Number
                                            {sortColumn === 'poNumber' && (
                                                sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Box
                                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                            onClick={() => handleSort('accountName')}
                                        >
                                            Account Name
                                            {sortColumn === 'accountName' && (
                                                sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Box
                                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                            onClick={() => handleSort('productName')}
                                        >
                                            Product Name
                                            {sortColumn === 'productName' && (
                                                sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Box
                                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                            onClick={() => handleSort('poValue')}
                                        >
                                            PO Value
                                            {sortColumn === 'poValue' && (
                                                sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Box
                                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                            onClick={() => handleSort('grandTotalINR')}
                                        >
                                            Total (INR)
                                            {sortColumn === 'grandTotalINR' && (
                                                sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Box
                                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                            onClick={() => handleSort('status')}
                                        >
                                            Status
                                            {sortColumn === 'status' && (
                                                sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell>Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {displayData.map((po) => (
                                    <TableRow key={po.id} hover>
                                        <TableCell>{po.poNumber}</TableCell>
                                        <TableCell>{po.accountName}</TableCell>
                                        <TableCell>{po.productName}</TableCell>
                                        <TableCell>
                                            {po.poValue.toLocaleString('en-IN', {
                                                style: 'currency',
                                                currency: po.poCurrency
                                            })}
                                        </TableCell>
                                        <TableCell>
                                            {formatCurrency(po.grandTotalINR)}
                                        </TableCell>
                                        <TableCell>
                                            <Chip
                                                label={po.status}
                                                color={getStatusChipColor(po.status)}
                                                size="small"
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <ActionButtons
                                                purchaseOrder={po}
                                                onView={handleView}
                                                onEdit={handleEdit}
                                                onDelete={handleDelete}
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    )}
                </TableContainer>
            </Box>
        </ThemeProvider>
    );
};

export default Purchase;
                    {/* Sidebar Drawer for Advanced Search and Table Settings */}
