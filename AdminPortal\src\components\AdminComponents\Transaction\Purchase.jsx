import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, Avatar, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, LocalShipping, ShowChart, PieChart, DonutLarge
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// --- MOCK DATA ---
const initialPurchases = [
    { id: 1, orderId: 'PO12345', item: 'Software License', quantity: 10, price: '$5,000', status: 'Completed', poStartDate: '2024-01-01', poEndDate: '2024-12-31' },
    { id: 2, orderId: 'PO12346', item: 'Hardware Upgrade', quantity: 2, price: '$1,500', status: 'Shipped', poStartDate: '2024-02-15', poEndDate: '2024-08-15' },
    { id: 3, orderId: 'PO12347', item: 'Consulting Services', quantity: 20, price: '$2,000', status: 'Pending', poStartDate: '2024-03-01', poEndDate: '2024-09-01' },
];

const ALL_COLUMNS = [
    { key: 'orderId', label: 'Order ID', type: 'string' },
    { key: 'item', label: 'Item', type: 'string' },
    { key: 'quantity', label: 'Quantity', type: 'number' },
    { key: 'price', label: 'Price', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'poStartDate', label: 'PO Start Date', type: 'string' },
    { key: 'poEndDate', label: 'PO End Date', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ purchase, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(purchase, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(purchase, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([purchase.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const PurchaseTable = ({ purchases, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < purchases.length} checked={purchases.length > 0 && selectedIds.length === purchases.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {purchases.map(p => (
                        <TableRow key={p.id} hover selected={selectedId === p.id} onClick={() => onRowClick(p)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(p.id)} onChange={() => onSelectOne(p.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{p[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons purchase={p} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const PurchaseCard = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(purchase.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{purchase.item}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{purchase.orderId}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Quantity:</strong> {purchase.quantity}</Typography>
            <Typography variant="body2"><strong>Price:</strong> {purchase.price}</Typography>
            <Typography variant="body2"><strong>PO Start:</strong> {purchase.poStartDate}</Typography>
            <Typography variant="body2"><strong>PO End:</strong> {purchase.poEndDate}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const PurchaseCompactCard = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(purchase.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{purchase.item}</Typography>
                <Typography variant="caption" color="text.secondary">{purchase.orderId}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{purchase.status}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const PurchaseListItem = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(purchase.id)} />
            <Box>
                <Typography fontWeight="bold">{purchase.item}</Typography>
                <Typography variant="body2" color="text.secondary">{purchase.orderId}</Typography>
            </Box>
            <Typography variant="body2">{purchase.quantity}</Typography>
            <Typography variant="body2">{purchase.status}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const PurchaseGraph = ({ purchase, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && purchase) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Quantity'],
                    datasets: [{
                        label: 'Order Quantity',
                        data: [purchase.quantity],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${purchase.item} - Quantity` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [purchase, chartType]);

    return (
        <>
            {purchase ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a purchase order to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const Purchase = () => {
    const [purchases, setPurchases] = useState(initialPurchases);
    const [selectedPurchase, setSelectedPurchase] = useState(null);
    const [sortColumn, setSortColumn] = useState('orderId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [viewMode, setViewMode] = useState('cards');
    const [isGraphVisible, setIsGraphVisible] = useState(true);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new purchase order', target: 'PO12345', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Approved purchase order', target: 'PO12346', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Finance', action: 'Marked as shipped', target: 'PO12346', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [chartType, setChartType] = useState('bar');
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
    // Quick Filter options (status and item)
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(purchases.map(p => p.status))];
        const items = [...new Set(purchases.map(p => p.item))];
        return [...statuses, ...items];
    }, [purchases]);
    const handleAddQuickFilter = (value) => {
        const statusValues = ['Completed', 'Shipped', 'Pending'];
        const field = statusValues.includes(value) ? 'status' : 'item';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    // Advanced Search Handlers
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const processedPurchases = useMemo(() => {
        let current = purchases;
        if (filter !== 'all') current = current.filter(p => p.status.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(p => p.orderId.toLowerCase().includes(term) || p.item.toLowerCase().includes(term));
        }
        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(purchase => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const purchaseValue = String(purchase[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return purchaseValue === filterValue;
                        case 'Not Equals': return purchaseValue !== filterValue;
                        case 'Contains': return purchaseValue.includes(filterValue);
                        case 'Starts With': return purchaseValue.startsWith(filterValue);
                        case 'Ends With': return purchaseValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        // Sorting
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [purchases, filter, searchTerm, sortColumn, sortDirection, activeFilters]);

    const summaryStats = useMemo(() => ({
        total: purchases.length,
        completed: purchases.filter(p => p.status === 'Completed').length,
        shipped: purchases.filter(p => p.status === 'Shipped').length,
        pending: purchases.filter(p => p.status === 'Pending').length,
    }), [purchases]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (purchase) => setSelectedPurchase(purchase);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedPurchases.map(p => p.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const displayPurchase = useMemo(() => {
        const isSelectedVisible = processedPurchases.some(p => p.id === selectedPurchase?.id);
        if (isSelectedVisible) return selectedPurchase;
        return processedPurchases.length > 0 ? processedPurchases[0] : null;
    }, [processedPurchases, selectedPurchase]);

    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedPurchases.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedPurchases.map(p => <PurchaseCard key={p.id} purchase={p} onClick={() => setSelectedPurchase(p)} isSelected={displayPurchase?.id === p.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(p.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <PurchaseTable
                            purchases={processedPurchases}
                            onRowClick={setSelectedPurchase}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={displayPurchase?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
                            onEdit={handleShowDetails}
                            onView={handleShowDetails}
                        />
                    )}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedPurchases.map(p => <PurchaseCompactCard key={p.id} purchase={p} onClick={() => setSelectedPurchase(p)} isSelected={displayPurchase?.id === p.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(p.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedPurchases.map(p => <PurchaseListItem key={p.id} purchase={p} onClick={() => setSelectedPurchase(p)} isSelected={displayPurchase?.id === p.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(p.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching Purchase Orders</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Orders</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'completed'} onClick={() => setFilter('completed')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.completed}</Typography><Typography variant="body2">Completed</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'shipped'} onClick={() => setFilter('shipped')}>
                                        <AdminComponents.SummaryAvatar variant="warning"><LocalShipping /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.shipped}</Typography><Typography variant="body2">Shipped</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'pending'} onClick={() => setFilter('pending')}>
                                        <AdminComponents.SummaryAvatar variant="warning"><Schedule /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.pending}</Typography><Typography variant="body2">Pending</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />}>Add Purchase Order</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedPurchases.length > 0 && selectedIds.length === processedPurchases.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedPurchases.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <PurchaseGraph purchase={displayPurchase} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                        <ActivityLog logs={activityLog} />
                    </AdminComponents.MainContentArea>
                    {/* Sidebar Drawer for Advanced Search and Table Settings */}
                    <Drawer
                        variant="persistent"
                        anchor="right"
                        open={isSidebarOpen}
                    >
                        <AdminComponents.SidebarContainer>
                            <AdminComponents.SidebarHeader>
                                <Typography variant="h6">
                                    {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                                </Typography>
                                <IconButton onClick={() => setIsSidebarOpen(false)}>
                                    <Cancel />
                                </IconButton>
                            </AdminComponents.SidebarHeader>
                            <AdminComponents.SidebarContent>
                                {sidebarMode === 'search' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.QuickFilterContainer>
                                                {quickFilterOptions.map(opt => (
                                                    <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                                ))}
                                            </AdminComponents.QuickFilterContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Field</InputLabel>
                                                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                    {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Operator</InputLabel>
                                                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                    {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                            <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {activeFilters.length > 0 ? activeFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.ColumnActionContainer>
                                                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                            </AdminComponents.ColumnActionContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                    <Chip
                                                        key={key}
                                                        label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                        onDelete={() => handleGroupByChange(key)}
                                                    />
                                                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={
                                                            <Checkbox
                                                                checked={groupByKeys.includes(col.key)}
                                                                onChange={() => handleGroupByChange(col.key)}
                                                            />
                                                        }
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                            </AdminComponents.SidebarContent>
                            <AdminComponents.SidebarFooter>
                                {sidebarMode === 'search' && (
                                    <>
                                        <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                        <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                                )}
                            </AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Purchase; 