import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Dialog, DialogContent, DialogActions, Grid, Card, CardContent, Divider,
    TableContainer, TablePagination, Menu, ListItemIcon, ListItemText, Alert, CircularProgress,
    InputAdornment, Tooltip, DialogTitle
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, LocalShipping, ShowChart, PieChart, DonutLarge,
    <PERSON><PERSON>s, MoreVert, ArrowUp<PERSON>, ArrowDownward, Assessment, TrendingUp, Close, Save
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';
import PurchaseOrderForm from './PurchaseOrderForm';
import PurchaseOrderCharts from './PurchaseOrderCharts';
import { calculateDerivedValues, emptyPurchaseOrder, validatePurchaseOrder } from '../../../models/PurchaseOrderModel';
import purchaseOrderTranslations from '../../../locales/en/purchaseOrder.json';

// Translation helper function
const t = (key) => {
    const keys = key.split('.');
    let value = purchaseOrderTranslations;
    for (const k of keys) {
        value = value?.[k];
    }
    return value || key;
};

// Mock Purchase Order data using the comprehensive model
const initialPurchases = [
    {
        id: 'po-001',
        owner: 'John Smith',
        parentCompanyName: 'Acme Corporation',
        accountName: 'Acme Global Services',
        domainVertical: 'Manufacturing',
        region: 'North America',
        revenueType: 'License',
        poNumber: 'PO-2025-001',
        poCurrency: 'USD',
        poValue: 100000,
        conversionRate: 83.5,
        hclConversionRateUSD: 83.5,
        opportunityStage: 'Closed Won',
        frequencyOfRealization: 'Monthly',
        startDate: '2025-04-01',
        endDate: '2026-03-31',
        renewalUpsell: 'New',
        productRevenueCategory: 'Software',
        productName: 'HCL Commerce Suite',
        projectDescription: 'Enterprise e-commerce platform implementation',
        status: 'Approved',
        createdBy: 'Jane Doe',
        createdDate: '2024-03-15T10:30:00Z',
        lastModifiedBy: 'Jane Doe',
        lastModifiedDate: '2024-03-15T10:30:00Z',
        projectedApr25: 8333.33,
        projectedMay25: 8333.33,
        projectedJun25: 8333.33,
        projectedJul25: 8333.33,
        projectedAug25: 8333.33,
        projectedSep25: 8333.33,
        projectedOct25: 8333.33,
        projectedNov25: 8333.33,
        projectedDec25: 8333.33,
        projectedJan26: 8333.33,
        projectedFeb26: 8333.33,
        projectedMar26: 8333.37,
        actualApr25: 8333.33,
        actualMay25: 8333.33,
        actualJun25: 0,
        actualJul25: 0,
        actualAug25: 0,
        actualSep25: 0,
        actualOct25: 0,
        actualNov25: 0,
        actualDec25: 0,
        actualJan26: 0,
        actualFeb26: 0,
        actualMar26: 0
    },
    {
        id: 'po-002',
        owner: 'Sarah Johnson',
        parentCompanyName: 'TechGlobal Inc.',
        accountName: 'TechGlobal Solutions',
        domainVertical: 'Technology',
        region: 'Europe',
        revenueType: 'Services',
        poNumber: 'PO-2025-002',
        poCurrency: 'EUR',
        poValue: 75000,
        conversionRate: 90.2,
        hclConversionRateUSD: 83.5,
        opportunityStage: 'Closed Won',
        frequencyOfRealization: 'Quarterly',
        startDate: '2025-04-01',
        endDate: '2026-03-31',
        renewalUpsell: 'New',
        productRevenueCategory: 'Professional Services',
        productName: 'HCL Digital Experience',
        projectDescription: 'Digital transformation consulting services',
        status: 'Pending',
        createdBy: 'Mike Wilson',
        createdDate: '2024-03-10T14:45:00Z',
        lastModifiedBy: 'Mike Wilson',
        lastModifiedDate: '2024-03-10T14:45:00Z',
        projectedApr25: 16875,
        projectedMay25: 16875,
        projectedJun25: 16875,
        projectedJul25: 16875,
        projectedAug25: 16875,
        projectedSep25: 16875,
        projectedOct25: 16875,
        projectedNov25: 16875,
        projectedDec25: 16875,
        projectedJan26: 16875,
        projectedFeb26: 16875,
        projectedMar26: 16875,
        actualApr25: 16875,
        actualMay25: 0,
        actualJun25: 0,
        actualJul25: 0,
        actualAug25: 0,
        actualSep25: 0,
        actualOct25: 0,
        actualNov25: 0,
        actualDec25: 0,
        actualJan26: 0,
        actualFeb26: 0,
        actualMar26: 0
    },
    {
        id: 'po-003',
        owner: 'David Chen',
        parentCompanyName: 'Asia Pacific Ltd.',
        accountName: 'APAC Enterprise',
        domainVertical: 'Finance',
        region: 'Asia Pacific',
        revenueType: 'License',
        poNumber: 'PO-2025-003',
        poCurrency: 'USD',
        poValue: 50000,
        conversionRate: 1,
        hclConversionRateUSD: 83.5,
        opportunityStage: 'Negotiation/Review',
        frequencyOfRealization: 'Annually',
        startDate: '2025-06-01',
        endDate: '2026-05-31',
        renewalUpsell: 'Renewal',
        productRevenueCategory: 'Software',
        productName: 'HCL Connections',
        projectDescription: 'Social collaboration platform renewal',
        status: 'Draft',
        createdBy: 'Lisa Wang',
        createdDate: '2024-03-20T09:15:00Z',
        lastModifiedBy: 'Lisa Wang',
        lastModifiedDate: '2024-03-20T09:15:00Z',
        projectedApr25: 0,
        projectedMay25: 0,
        projectedJun25: 4166.67,
        projectedJul25: 4166.67,
        projectedAug25: 4166.67,
        projectedSep25: 4166.67,
        projectedOct25: 4166.67,
        projectedNov25: 4166.67,
        projectedDec25: 4166.67,
        projectedJan26: 4166.67,
        projectedFeb26: 4166.67,
        projectedMar26: 4166.67,
        actualApr25: 0,
        actualMay25: 0,
        actualJun25: 0,
        actualJul25: 0,
        actualAug25: 0,
        actualSep25: 0,
        actualOct25: 0,
        actualNov25: 0,
        actualDec25: 0,
        actualJan26: 0,
        actualFeb26: 0,
        actualMar26: 0
    }
].map(po => calculateDerivedValues(po));

// Column definitions using internationalization
const ALL_COLUMNS = [
    { key: 'poNumber', label: t('labels.poNumber'), type: 'string', groupable: true },
    { key: 'accountName', label: t('labels.accountName'), type: 'string', groupable: true },
    { key: 'productName', label: t('labels.productName'), type: 'string', groupable: true },
    { key: 'poValue', label: t('labels.poValue'), type: 'number', groupable: false },
    { key: 'grandTotalINR', label: t('labels.grandTotalINR'), type: 'number', groupable: false },
    { key: 'status', label: 'Status', type: 'string', groupable: true },
    { key: 'region', label: t('labels.region'), type: 'string', groupable: true },
    { key: 'revenueType', label: t('labels.revenueType'), type: 'string', groupable: true },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ purchase, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(purchase, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(purchase, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([purchase.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const PurchaseTable = ({ purchases, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < purchases.length} checked={purchases.length > 0 && selectedIds.length === purchases.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {purchases.map(p => (
                        <TableRow key={p.id} hover selected={selectedId === p.id} onClick={() => onRowClick(p)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(p.id)} onChange={() => onSelectOne(p.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{p[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons purchase={p} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const PurchaseCard = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(purchase.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{purchase.item}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{purchase.orderId}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Quantity:</strong> {purchase.quantity}</Typography>
            <Typography variant="body2"><strong>Price:</strong> {purchase.price}</Typography>
            <Typography variant="body2"><strong>PO Start:</strong> {purchase.poStartDate}</Typography>
            <Typography variant="body2"><strong>PO End:</strong> {purchase.poEndDate}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const PurchaseCompactCard = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(purchase.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{purchase.item}</Typography>
                <Typography variant="caption" color="text.secondary">{purchase.orderId}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{purchase.status}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const PurchaseListItem = ({ purchase, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(purchase.id)} />
            <Box>
                <Typography fontWeight="bold">{purchase.item}</Typography>
                <Typography variant="body2" color="text.secondary">{purchase.orderId}</Typography>
            </Box>
            <Typography variant="body2">{purchase.quantity}</Typography>
            <Typography variant="body2">{purchase.status}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: purchase.status }} label={purchase.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons purchase={purchase} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const PurchaseGraph = ({ purchase, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && purchase) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Quantity'],
                    datasets: [{
                        label: 'Order Quantity',
                        data: [purchase.quantity],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${purchase.item} - Quantity` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [purchase, chartType]);

    return (
        <>
            {purchase ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a purchase order to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const Purchase = () => {
    // State management for unified Purchase Order interface
    const [purchaseOrders, setPurchaseOrders] = useState(initialPurchases);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [sortBy, setSortBy] = useState('lastModifiedDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [selectedPO, setSelectedPO] = useState(null);
    const [formDialogOpen, setFormDialogOpen] = useState(false);
    const [formMode, setFormMode] = useState(null); // null, 'create', 'edit', 'view'
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
    const [activeStatusTab, setActiveStatusTab] = useState(0); // Status filter tabs
    const [viewMode, setViewMode] = useState('table'); // table, cards, compact, list
    const [selectedIds, setSelectedIds] = useState([]);
    const [filters, setFilters] = useState({
        status: null,
        region: null,
        revenueType: null
    });

    // Calculate summary statistics
    const summaryStats = useMemo(() => {
        const totalPOs = purchaseOrders.length;
        const totalValue = purchaseOrders.reduce((sum, po) => sum + (po.grandTotalINR || 0), 0);
        const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

        const statusCounts = purchaseOrders.reduce((acc, po) => {
            acc[po.status] = (acc[po.status] || 0) + 1;
            return acc;
        }, {});

        return { totalPOs, totalValue, avgValue, statusCounts };
    }, [purchaseOrders]);

    // Handle search and filtering
    const filteredPurchaseOrders = useCallback(() => {
        return purchaseOrders.filter(po => {
            // Search term filtering
            const searchLower = searchTerm.toLowerCase();
            const matchesSearch =
                !searchTerm ||
                po.poNumber.toLowerCase().includes(searchLower) ||
                po.accountName.toLowerCase().includes(searchLower) ||
                po.parentCompanyName.toLowerCase().includes(searchLower) ||
                po.productName.toLowerCase().includes(searchLower) ||
                po.owner.toLowerCase().includes(searchLower);

            // Status filtering
            const matchesStatus = !filters.status || po.status === filters.status;

            // Region filtering
            const matchesRegion = !filters.region || po.region === filters.region;

            // Revenue Type filtering
            const matchesRevenueType = !filters.revenueType || po.revenueType === filters.revenueType;

            return matchesSearch && matchesStatus && matchesRegion && matchesRevenueType;
        }).sort((a, b) => {
            // Sorting
            const aValue = a[sortBy];
            const bValue = b[sortBy];

            if (typeof aValue === 'string') {
                return sortDirection === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            } else {
                return sortDirection === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            }
        });
    }, [purchaseOrders, searchTerm, sortBy, sortDirection, filters]);

    // Event handlers
    const handleSort = (field) => {
        if (sortBy === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(field);
            setSortDirection('asc');
        }
    };

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleStatusTabChange = (event, newValue) => {
        setActiveStatusTab(newValue);
        // Set filters based on status tab
        switch(newValue) {
            case 0: // All
                setFilters(prev => ({ ...prev, status: null }));
                break;
            case 1: // Draft
                setFilters(prev => ({ ...prev, status: 'Draft' }));
                break;
            case 2: // Pending
                setFilters(prev => ({ ...prev, status: 'Pending' }));
                break;
            case 3: // Approved
                setFilters(prev => ({ ...prev, status: 'Approved' }));
                break;
            case 4: // Completed
                setFilters(prev => ({ ...prev, status: 'Completed' }));
                break;
            default:
                setFilters(prev => ({ ...prev, status: null }));
        }
    };

    const handleActionMenuOpen = (event, po) => {
        setSelectedPO(po);
        setActionMenuAnchor(event.currentTarget);
    };

    const handleActionMenuClose = () => {
        setActionMenuAnchor(null);
    };

    const handleCreatePO = () => {
        setSelectedPO(null);
        setFormMode('create');
        setFormDialogOpen(true);
    };

    const handleEditPO = (po) => {
        setSelectedPO(po);
        setFormMode('edit');
        setFormDialogOpen(true);
        handleActionMenuClose();
    };

    const handleViewPO = (po) => {
        setSelectedPO(po);
        setFormMode('view');
        setFormDialogOpen(true);
        handleActionMenuClose();
    };

    const handleDeleteClick = (po) => {
        setSelectedPO(po);
        setDeleteDialogOpen(true);
        handleActionMenuClose();
    };

    const handleDeleteConfirm = () => {
        setPurchaseOrders(prev => prev.filter(po => po.id !== selectedPO.id));
        setDeleteDialogOpen(false);
        setSelectedPO(null);
    };

    const handleDeleteCancel = () => {
        setDeleteDialogOpen(false);
        setSelectedPO(null);
    };

    const handleFormCancel = () => {
        setFormDialogOpen(false);
        setSelectedPO(null);
        setFormMode(null);
    };

    const handleFormSave = (formData) => {
        if (formMode === 'create') {
            const newPO = {
                ...formData,
                id: `po-${Date.now()}`,
                createdBy: 'Current User',
                createdDate: new Date().toISOString(),
            };
            setPurchaseOrders(prev => [...prev, calculateDerivedValues(newPO)]);
        } else if (formMode === 'edit') {
            setPurchaseOrders(prev =>
                prev.map(po => po.id === formData.id ? calculateDerivedValues(formData) : po)
            );
        }

        setFormDialogOpen(false);
        setSelectedPO(null);
        setFormMode(null);
    };

    const getStatusChipColor = (status) => {
        switch(status) {
            case 'Draft': return 'default';
            case 'Pending': return 'warning';
            case 'Approved': return 'success';
            case 'Rejected': return 'error';
            case 'Completed': return 'primary';
            case 'Cancelled': return 'error';
            default: return 'default';
        }
    };

    const formatCurrency = (value, currency = 'INR') => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    };

    // Get filtered data for display
    const displayData = filteredPurchaseOrders();

    // Unified Purchase Order Management Interface
    return (
        <ThemeProvider theme={theme}>
            <Box sx={{ p: 3 }}>
                <Typography variant="h4" component="h1" sx={{ mb: 3 }}>
                    {t('title')}
                </Typography>

                {/* Status Filter Tabs */}
                <Tabs
                    value={activeStatusTab}
                    onChange={handleStatusTabChange}
                    sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}
                    variant="scrollable"
                    scrollButtons="auto"
                >
                    <Tab label="All" />
                    <Tab label="Draft" />
                    <Tab label="Pending" />
                    <Tab label="Approved" />
                    <Tab label="Completed" />
                </Tabs>

                {/* Summary Cards Section */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                    <Grid item xs={12} sm={6} md={3}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom variant="body2">
                                            Total Purchase Orders
                                        </Typography>
                                        <Typography variant="h4" component="div">
                                            {summaryStats.totalPOs}
                                        </Typography>
                                    </Box>
                                    <Assessment color="primary" sx={{ fontSize: 40 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom variant="body2">
                                            Total Value
                                        </Typography>
                                        <Typography variant="h5" component="div">
                                            {formatCurrency(summaryStats.totalValue)}
                                        </Typography>
                                    </Box>
                                    <TrendingUp color="success" sx={{ fontSize: 40 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom variant="body2">
                                            Average Value
                                        </Typography>
                                        <Typography variant="h5" component="div">
                                            {formatCurrency(summaryStats.avgValue)}
                                        </Typography>
                                    </Box>
                                    <BarChart color="info" sx={{ fontSize: 40 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                        <Card>
                            <CardContent>
                                <Box>
                                    <Typography color="textSecondary" gutterBottom variant="body2">
                                        Status Distribution
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                                        {Object.entries(summaryStats.statusCounts).map(([status, count]) => (
                                            <Chip
                                                key={status}
                                                label={`${status}: ${count}`}
                                                color={getStatusChipColor(status)}
                                                size="small"
                                            />
                                        ))}
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>

                {/* Action Bar */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <TextField
                            placeholder={t('buttons.search')}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            size="small"
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <Search />
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <ToggleButtonGroup
                            value={viewMode}
                            exclusive
                            onChange={(e, newMode) => newMode && setViewMode(newMode)}
                            size="small"
                        >
                            <ToggleButton value="table" title="Table View">
                                <ViewList />
                            </ToggleButton>
                            <ToggleButton value="cards" title="Card View">
                                <ViewModule />
                            </ToggleButton>
                            <ToggleButton value="compact" title="Compact View">
                                <GridView />
                            </ToggleButton>
                        </ToggleButtonGroup>
                    </Box>

                    <Button
                        variant="contained"
                        startIcon={<Add />}
                        onClick={handleCreatePO}
                    >
                        {t('buttons.add')}
                    </Button>
                </Box>

                {/* Purchase Orders Table/Cards */}
                {viewMode === 'table' && (
                    <TableContainer component={Paper} sx={{ mb: 3 }}>
                        {loading ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                                <CircularProgress />
                            </Box>
                        ) : displayData.length === 0 ? (
                            <Box sx={{ p: 4, textAlign: 'center' }}>
                                <Typography variant="body1">{t('messages.noData')}</Typography>
                            </Box>
                        ) : (
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell>
                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                                onClick={() => handleSort('poNumber')}
                                            >
                                                {t('labels.poNumber')}
                                                {sortBy === 'poNumber' && (
                                                    sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                                onClick={() => handleSort('accountName')}
                                            >
                                                {t('labels.accountName')}
                                                {sortBy === 'accountName' && (
                                                    sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                                onClick={() => handleSort('productName')}
                                            >
                                                {t('labels.productName')}
                                                {sortBy === 'productName' && (
                                                    sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                                onClick={() => handleSort('poValue')}
                                            >
                                                {t('labels.poValue')}
                                                {sortBy === 'poValue' && (
                                                    sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                                onClick={() => handleSort('grandTotalINR')}
                                            >
                                                {t('labels.grandTotalINR')}
                                                {sortBy === 'grandTotalINR' && (
                                                    sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                                onClick={() => handleSort('status')}
                                            >
                                                Status
                                                {sortBy === 'status' && (
                                                    sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>Actions</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {displayData
                                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                        .map((po) => (
                                            <TableRow key={po.id} hover>
                                                <TableCell>{po.poNumber}</TableCell>
                                                <TableCell>{po.accountName}</TableCell>
                                                <TableCell>{po.productName}</TableCell>
                                                <TableCell>
                                                    {po.poValue.toLocaleString('en-IN', {
                                                        style: 'currency',
                                                        currency: po.poCurrency
                                                    })}
                                                </TableCell>
                                                <TableCell>
                                                    {formatCurrency(po.grandTotalINR)}
                                                </TableCell>
                                                <TableCell>
                                                    <Chip
                                                        label={po.status}
                                                        color={getStatusChipColor(po.status)}
                                                        size="small"
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <IconButton onClick={(e) => handleActionMenuOpen(e, po)}>
                                                        <MoreVert />
                                                    </IconButton>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                </TableBody>
                            </Table>
                        )}

                        <TablePagination
                            rowsPerPageOptions={[5, 10, 25]}
                            component="div"
                            count={displayData.length}
                            rowsPerPage={rowsPerPage}
                            page={page}
                            onPageChange={handleChangePage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                        />
                    </TableContainer>
                )}

                {/* Analytics Charts Section */}
                <PurchaseOrderCharts purchaseOrders={purchaseOrders} />

                {/* Action Menu */}
                <Menu
                    anchorEl={actionMenuAnchor}
                    open={Boolean(actionMenuAnchor)}
                    onClose={handleActionMenuClose}
                >
                    <MenuItem onClick={() => handleViewPO(selectedPO)}>
                        <ListItemIcon>
                            <Visibility fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>{t('buttons.view')}</ListItemText>
                    </MenuItem>
                    <MenuItem onClick={() => handleEditPO(selectedPO)}>
                        <ListItemIcon>
                            <Edit fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>{t('buttons.edit')}</ListItemText>
                    </MenuItem>
                    <MenuItem onClick={() => handleDeleteClick(selectedPO)}>
                        <ListItemIcon>
                            <Delete fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>{t('buttons.delete')}</ListItemText>
                    </MenuItem>
                </Menu>

                {/* Purchase Order Form Dialog */}
                <AdminComponents.DialogHeader open={formDialogOpen} onClose={handleFormCancel} fullWidth maxWidth="lg">
                    <AdminComponents.DialogProgressContainer>
                        <Typography variant="h6">
                            {formMode === 'create' ? 'Create' : formMode === 'edit' ? 'Edit' : 'View'} {t('title')}
                        </Typography>
                    </AdminComponents.DialogProgressContainer>
                    <IconButton onClick={handleFormCancel} sx={{ color: 'white' }}>
                        <Close />
                    </IconButton>
                </AdminComponents.DialogHeader>

                {formDialogOpen && (
                    <AdminComponents.DialogContent sx={{ p: 0, overflow: 'unset' }}>
                        <AdminComponents.FixedHeightDialogContent>
                            <PurchaseOrderForm
                                purchaseOrder={selectedPO}
                                onSave={handleFormSave}
                                onCancel={handleFormCancel}
                                mode={formMode}
                            />
                        </AdminComponents.FixedHeightDialogContent>
                    </AdminComponents.DialogContent>
                )}

                {formDialogOpen && (
                    <AdminComponents.DialogFooter>
                        <AdminComponents.DialogSummary>
                            <Typography variant="body2"><strong>Status:</strong> {selectedPO?.status || 'Draft'}</Typography>
                            <Typography variant="body2"><strong>Currency:</strong> {selectedPO?.poCurrency || 'INR'}</Typography>
                        </AdminComponents.DialogSummary>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                            <Button onClick={handleFormCancel}>
                                {t('buttons.cancel')}
                            </Button>
                            {formMode !== 'view' && (
                                <Button variant="contained" startIcon={<Save />}>
                                    {t('buttons.save')}
                                </Button>
                            )}
                        </Box>
                    </AdminComponents.DialogFooter>
                )}

                {/* Delete Confirmation Dialog */}
                <AdminComponents.DialogHeader
                    open={deleteDialogOpen}
                    onClose={handleDeleteCancel}
                >
                    <AdminComponents.DialogProgressContainer>
                        <Typography variant="h6">Confirm Delete</Typography>
                    </AdminComponents.DialogProgressContainer>
                </AdminComponents.DialogHeader>

                {deleteDialogOpen && (
                    <AdminComponents.DialogContent>
                        <Typography>
                            {t('messages.confirmDelete')}
                        </Typography>
                    </AdminComponents.DialogContent>
                )}

                {deleteDialogOpen && (
                    <AdminComponents.DialogFooter>
                        <Box sx={{ display: 'flex', gap: 2, ml: 'auto' }}>
                            <Button onClick={handleDeleteCancel}>Cancel</Button>
                            <Button onClick={handleDeleteConfirm} color="error" variant="contained">
                                Delete
                            </Button>
                        </Box>
                    </AdminComponents.DialogFooter>
                )}
            </Box>
        </ThemeProvider>
    );
};

export default Purchase;
                                            <AdminComponents.QuickFilterContainer>
                                                {quickFilterOptions.map(opt => (
                                                    <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                                ))}
                                            </AdminComponents.QuickFilterContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Field</InputLabel>
                                                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                    {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Operator</InputLabel>
                                                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                    {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                            <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {activeFilters.length > 0 ? activeFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.ColumnActionContainer>
                                                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                            </AdminComponents.ColumnActionContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                    <Chip
                                                        key={key}
                                                        label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                        onDelete={() => handleGroupByChange(key)}
                                                    />
                                                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={
                                                            <Checkbox
                                                                checked={groupByKeys.includes(col.key)}
                                                                onChange={() => handleGroupByChange(col.key)}
                                                            />
                                                        }
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                            </AdminComponents.SidebarContent>
                            <AdminComponents.SidebarFooter>
                                {sidebarMode === 'search' && (
                                    <>
                                        <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                        <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                                )}
                            </AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Purchase; 