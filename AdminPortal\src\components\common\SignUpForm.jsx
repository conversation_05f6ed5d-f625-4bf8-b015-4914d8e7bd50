import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// import './SignUpForm.css';
import Button from '@mui/material/Button';

const countryList = [
  'United States', 'India', 'United Kingdom', 'Germany', 'France', 'Canada', 'Australia', 'Singapore', 'Japan', 'Other'
];

const shakeAnim = {
  initial: { x: 0 },
  animate: { x: [0, -8, 8, -6, 6, -3, 3, 0] },
  transition: { duration: 0.38, ease: 'easeInOut' }
};

const SignUpForm = ({ onSuccess, onCancel }) => {
  const [step, setStep] = useState(1);
  const [fields, setFields] = useState({
    firstName: '', lastName: '', email: '', company: '', jobTitle: '', phone: '', country: '', password: '', confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [shake, setShake] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  // For staggered animation
  const stagger = 0.08;

  const handleChange = e => {
    setFields({ ...fields, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: '' });
    setShake({ ...shake, [e.target.name]: false });
  };

  const handleContinue = e => {
    e.preventDefault();
    let newErrors = {};
    let newShake = {};
    ['firstName', 'lastName', 'email', 'company', 'jobTitle', 'phone', 'country'].forEach(f => {
      if (!fields[f]) {
        newErrors[f] = 'Required.';
        newShake[f] = true;
      }
    });
    setErrors(newErrors);
    setShake(newShake);
    if (Object.keys(newErrors).length > 0) return;
    setStep(2);
  };

  const handleBack = e => {
    e.preventDefault();
    setStep(1);
  };

  const handleSubmit = e => {
    e.preventDefault();
    let newErrors = {};
    let newShake = {};
    ['password', 'confirmPassword'].forEach(f => {
      if (!fields[f]) {
        newErrors[f] = 'Required.';
        newShake[f] = true;
      }
    });
    if (fields.password && fields.confirmPassword && fields.password !== fields.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match.';
      newShake.confirmPassword = true;
    }
    setErrors(newErrors);
    setShake(newShake);
    if (Object.keys(newErrors).length > 0) return;
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      onSuccess && onSuccess();
    }, 1200);
  };

  return (
    <div className="signup-form-wrapper">
      <div className="signup-step-indicator">Step {step} of 2</div>
      <AnimatePresence mode="wait" initial={false}>
        {step === 1 && (
          <motion.form
            key="step1"
            className="signup-form"
            initial={{ x: 0, opacity: 1 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -60, opacity: 0 }}
            transition={{ duration: 0.32, ease: 'easeInOut' }}
            autoComplete="off"
            onSubmit={handleContinue}
          >
            <motion.div
              className="signup-fields signup-grid"
              initial="hidden"
              animate="visible"
              variants={{
                visible: { transition: { staggerChildren: stagger } },
                hidden: {}
              }}
            >
              {/* Row 1: First Name | Last Name */}
              <motion.div variants={shake['firstName'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, firstName: false }))}>
                <div className="floating-label-group">
                  <input type="text" name="firstName" value={fields.firstName} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.firstName ? 'filled' : ''}>First Name</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.firstName && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.firstName}</motion.div>}</AnimatePresence>
              </motion.div>
              <motion.div variants={shake['lastName'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, lastName: false }))}>
                <div className="floating-label-group">
                  <input type="text" name="lastName" value={fields.lastName} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.lastName ? 'filled' : ''}>Last Name</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.lastName && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.lastName}</motion.div>}</AnimatePresence>
              </motion.div>
              {/* Row 2: Business Email | Company */}
              <motion.div variants={shake['email'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, email: false }))}>
                <div className="floating-label-group">
                  <input type="email" name="email" value={fields.email} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.email ? 'filled' : ''}>Business Email</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.email && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.email}</motion.div>}</AnimatePresence>
              </motion.div>
              <motion.div variants={shake['company'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, company: false }))}>
                <div className="floating-label-group">
                  <input type="text" name="company" value={fields.company} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.company ? 'filled' : ''}>Company</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.company && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.company}</motion.div>}</AnimatePresence>
              </motion.div>
              {/* Row 3: Job Title | Phone */}
              <motion.div variants={shake['jobTitle'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, jobTitle: false }))}>
                <div className="floating-label-group">
                  <input type="text" name="jobTitle" value={fields.jobTitle} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.jobTitle ? 'filled' : ''}>Job Title</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.jobTitle && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.jobTitle}</motion.div>}</AnimatePresence>
              </motion.div>
              <motion.div variants={shake['phone'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, phone: false }))}>
                <div className="floating-label-group">
                  <input type="text" name="phone" value={fields.phone} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.phone ? 'filled' : ''}>Phone/Mobile</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.phone && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.phone}</motion.div>}</AnimatePresence>
              </motion.div>
              {/* Row 4: Country (full-width) */}
              <motion.div className="signup-grid-full" variants={shake['country'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, country: false }))}>
                <div className="floating-label-group">
                  <select name="country" value={fields.country} onChange={handleChange} required className="floating-input">
                    <option value="" disabled>Select Country</option>
                    {countryList.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                  <label className={fields.country ? 'filled' : ''}>Country</label>
                  <span className="input-border-anim" />
                </div>
                <AnimatePresence>{errors.country && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.country}</motion.div>}</AnimatePresence>
              </motion.div>
            </motion.div>
            <div className="signup-form-actions">
              <Button type="submit" variant="contained" color="primary" fullWidth sx={{ borderRadius: 50, fontWeight: 700 }}>
                {step === 2 ? 'Sign Up' : 'Continue'}
              </Button>
            </div>
          </motion.form>
        )}
        {step === 2 && (
          <motion.form
            key="step2"
            className="signup-form"
            initial={{ x: 60, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 60, opacity: 0 }}
            transition={{ duration: 0.32, ease: 'easeInOut' }}
            autoComplete="off"
            onSubmit={handleSubmit}
          >
            <motion.div
              className="signup-fields signup-grid"
              initial="hidden"
              animate="visible"
              variants={{
                visible: { transition: { staggerChildren: stagger } },
                hidden: {}
              }}
            >
              {/* Username (full-width, read-only) */}
              <motion.div className="signup-grid-full">
                <div className="floating-label-group">
                  <input type="text" name="username" value={fields.email} readOnly className="floating-input" />
                  <label className="filled">Username (Business Email)</label>
                </div>
              </motion.div>
              {/* Password | Confirm Password */}
              <motion.div variants={shake['password'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, password: false }))}>
                <div className="floating-label-group password-group">
                  <input type={showPassword ? 'text' : 'password'} name="password" value={fields.password} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.password ? 'filled' : ''}>Create Password</label>
                  <span className="input-border-anim" />
                  <button type="button" className="show-hide-toggle" tabIndex={0} aria-label={showPassword ? 'Hide password' : 'Show password'} onClick={() => setShowPassword(v => !v)}>
                    {showPassword ? (
                      <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#00b6b6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z"/><circle cx="12" cy="12" r="3"/></svg>
                    ) : (
                      <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#00b6b6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17.94 17.94A10.94 10.94 0 0 1 12 19c-7 0-11-7-11-7a21.81 21.81 0 0 1 5.06-6.06"/><path d="M1 1l22 22"/><path d="M9.53 9.53A3 3 0 0 0 12 15a3 3 0 0 0 2.47-5.47"/></svg>
                    )}
                  </button>
                </div>
                <AnimatePresence>{errors.password && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.password}</motion.div>}</AnimatePresence>
              </motion.div>
              <motion.div variants={shake['confirmPassword'] ? shakeAnim : {}} onAnimationComplete={() => setShake(s => ({ ...s, confirmPassword: false }))}>
                <div className="floating-label-group password-group">
                  <input type={showConfirm ? 'text' : 'password'} name="confirmPassword" value={fields.confirmPassword} onChange={handleChange} required className="floating-input" autoComplete="off" />
                  <label className={fields.confirmPassword ? 'filled' : ''}>Confirm Password</label>
                  <span className="input-border-anim" />
                  <button type="button" className="show-hide-toggle" tabIndex={0} aria-label={showConfirm ? 'Hide password' : 'Show password'} onClick={() => setShowConfirm(v => !v)}>
                    {showConfirm ? (
                      <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#00b6b6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z"/><circle cx="12" cy="12" r="3"/></svg>
                    ) : (
                      <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#00b6b6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17.94 17.94A10.94 10.94 0 0 1 12 19c-7 0-11-7-11-7a21.81 21.81 0 0 1 5.06-6.06"/><path d="M1 1l22 22"/><path d="M9.53 9.53A3 3 0 0 0 12 15a3 3 0 0 0 2.47-5.47"/></svg>
                    )}
                  </button>
                </div>
                <AnimatePresence>{errors.confirmPassword && <motion.div className="input-error-text" initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 6 }} transition={{ duration: 0.22 }}>{errors.confirmPassword}</motion.div>}</AnimatePresence>
              </motion.div>
              {/* Password strength meter (full-width) */}
              <motion.div className="signup-grid-full">
                <div className="password-strength-meter">
                  <div className="password-strength-bar" style={{ '--password-strength-width': `${Math.min(fields.password.length * 12, 100)}%` }} />
                </div>
              </motion.div>
            </motion.div>
            <div className="signup-form-actions">
              <Button type="button" variant="outlined" color="primary" onClick={handleBack} sx={{ borderRadius: 50, fontWeight: 700, mr: 2 }}>
                Back
              </Button>
              <Button type="submit" variant="contained" color="primary" fullWidth disabled={loading} sx={{ borderRadius: 50, fontWeight: 700 }}>
                {loading ? <span className="signup-spinner" /> : 'Sign Up'}
              </Button>
            </div>
          </motion.form>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SignUpForm; 