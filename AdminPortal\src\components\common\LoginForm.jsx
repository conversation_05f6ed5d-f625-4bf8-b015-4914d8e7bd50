import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// import './SignUpForm.css'; 
import googleIcon from '../../assets/Images/social-icons/google.png';
import microsoftIcon from '../../assets/Images/social-icons/microsoft.png';
import Button from '@mui/material/Button';

const shakeAnim = {
  initial: { x: 0 },
  animate: { x: [0, -8, 8, -6, 6, -3, 3, 0] },
  transition: { duration: 0.38, ease: 'easeInOut' }
};

const LoginForm = ({ onSuccess, onCancel, userType = 'user' }) => {
  const [fields, setFields] = useState({ email: '', password: '' });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [shake, setShake] = useState({ email: false, password: false });
  const [remember, setRemember] = useState(false);
  const [showForgot, setShowForgot] = useState(false);
  const [forgotEmail, setForgotEmail] = useState('');
  const [forgotError, setForgotError] = useState('');
  const [forgotSent, setForgotSent] = useState(false);
  const [loginMessage, setLoginMessage] = useState('');

  const handleChange = e => {
    setFields({ ...fields, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: '' });
    setShake({ ...shake, [e.target.name]: false });
  };

  const handleSubmit = e => {
    e.preventDefault();
    let newErrors = {};
    let newShake = {};
    if (!fields.email) {
      newErrors.email = 'Email is required.';
      newShake.email = true;
    }
    if (!fields.password) {
      newErrors.password = 'Password is required.';
      newShake.password = true;
    }
    setErrors(newErrors);
    setShake(newShake);
    if (Object.keys(newErrors).length > 0) return;
    setLoading(true);
    setLoginMessage('');
    setTimeout(() => {
      setLoading(false);
      const found = dummyAccounts.find(
        acc => acc.email === fields.email && acc.password === fields.password && acc.type === userType
      );
      if (found) {
        setLoginMessage('Login successful!');
        onSuccess && onSuccess(found);
      } else {
        setLoginMessage('Invalid username or password');
        setShake({ email: true, password: true });
      }
    }, 800);
  };

  // Social login click handlers (placeholder)
  const handleGoogle = () => alert('Google sign-in coming soon!');
  const handleMicrosoft = () => alert('Microsoft sign-in coming soon!');

  // Forgot password handlers
  const handleForgotSubmit = e => {
    e.preventDefault();
    setForgotError('');
    if (!forgotEmail) {
      setForgotError('Email is required.');
      return;
    }
    // Simulate sending email
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setForgotSent(true);
    }, 1200);
  };

  const dummyAccounts = [
    { email: '<EMAIL>', password: '12345', type: 'user',name: 'User',country: 'India',role: 'User',company: 'HCL Software',phone: '**********',address: '123, Main St, Anytown, USA',photo: 'src/assets/Images/social-icons/user.png' },
    { email: '<EMAIL>', password: '12345', type: 'admin',name: 'Admin',country: 'India',role: 'Admin',company: 'HCLTech',phone: '**********',address: '123, Main St, Anytown, USA',photo: 'src/assets/Images/social-icons/user.png' },
  ];

  return (
    <>
      <AnimatePresence mode="wait" initial={false}>
        {!showForgot ? (
          <motion.form
            key="login"
            className="signup-form"
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 24 }}
            transition={{ duration: 0.32 }}
            autoComplete="off"
            onSubmit={handleSubmit}
          >
            <motion.div
              className="signup-fields"
              initial="hidden"
              animate="visible"
              variants={{
                visible: { transition: { staggerChildren: 0.09 } },
                hidden: {}
              }}
            >
              <motion.div
                className="signup-field-row"
                variants={{ hidden: { opacity: 0, y: 24 }, visible: { opacity: 1, y: 0, transition: { duration: 0.32 } } }}
              >
                <motion.div
                  {...(shake.email ? shakeAnim : {})}
                  onAnimationComplete={() => setShake(s => ({ ...s, email: false }))}
                >
                  <div className="floating-label-group">
                    <input
                      type="email"
                      name="email"
                      value={fields.email}
                      onChange={handleChange}
                      required
                      className="floating-input"
                      autoComplete="off"
                    />
                    <label className={fields.email ? 'filled' : ''}>Email</label>
                    <span className="input-border-anim" />
                  </div>
                  <AnimatePresence>
                    {errors.email && (
                      <motion.div
                        className="input-error-text"
                        initial={{ opacity: 0, y: 6 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 6 }}
                        transition={{ duration: 0.22 }}
                      >
                        {errors.email}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </motion.div>
              <motion.div
                className="signup-field-row"
                variants={{ hidden: { opacity: 0, y: 24 }, visible: { opacity: 1, y: 0, transition: { duration: 0.32 } } }}
              >
                <motion.div
                  {...(shake.password ? shakeAnim : {})}
                  onAnimationComplete={() => setShake(s => ({ ...s, password: false }))}
                >
                  <div className="floating-label-group">
                    <input
                      type="password"
                      name="password"
                      value={fields.password}
                      onChange={handleChange}
                      required
                      className="floating-input"
                      autoComplete="off"
                    />
                    <label className={fields.password ? 'filled' : ''}>Password</label>
                    <span className="input-border-anim" />
                  </div>
                  <AnimatePresence>
                    {errors.password && (
                      <motion.div
                        className="input-error-text"
                        initial={{ opacity: 0, y: 6 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 6 }}
                        transition={{ duration: 0.22 }}
                      >
                        {errors.password}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </motion.div>
            </motion.div>
            <div className="login-form-row">
              <label className="remember-me">
                <input type="checkbox" checked={remember} onChange={e => setRemember(e.target.checked)} />
                <span className="custom-checkbox" /> Remember Me
              </label>
              <span className="forgot-password-link" onClick={() => setShowForgot(true)} tabIndex={0} role="button">Forgot Password?</span>
            </div>
            <Button type="submit" variant="contained" color="primary" fullWidth disabled={loading} sx={{ borderRadius: 50, fontWeight: 700 }}>
              {loading ? <span className="signup-spinner" /> : 'Log In'}
            </Button>
            {loginMessage && (
              <div className={`login-message${loginMessage === 'Login successful!' ? ' success' : ' error'}`}>{loginMessage}</div>
            )}
          </motion.form>
        ) : (
          <motion.form
            key="forgot"
            className="signup-form"
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 24 }}
            transition={{ duration: 0.32 }}
            autoComplete="off"
            onSubmit={handleForgotSubmit}
          >
            <div className="forgot-title">Forgot Password?</div>
            <div className="forgot-desc">Enter your email address to receive a password reset link.</div>
            <div className="floating-label-group">
              <input
                type="email"
                name="forgotEmail"
                value={forgotEmail}
                onChange={e => setForgotEmail(e.target.value)}
                required
                className="floating-input"
                autoComplete="off"
              />
              <label className={forgotEmail ? 'filled' : ''}>Email</label>
              <span className="input-border-anim" />
            </div>
            {forgotError && <div className="input-error-text">{forgotError}</div>}
            {forgotSent && <div className="forgot-success">A password reset link has been sent to your email.</div>}
            <Button type="submit" variant="contained" color="primary" fullWidth disabled={loading} sx={{ borderRadius: 50, fontWeight: 700, mt: 2 }}>
              {loading ? <span className="signup-spinner" /> : 'Submit'}
            </Button>
            <div className="auth-modal-switch-link auth-modal-switch-link-mt">
              Already have an account?{' '}
              <span onClick={() => setShowForgot(false)} tabIndex={0} role="button">Log In</span>
            </div>
          </motion.form>
        )}
      </AnimatePresence>
      {userType === 'user' && !showForgot && (
        <div className="social-login-options">
          <div className="social-login-divider"><span>or</span></div>
          <div className="social-login-row">
            <button className="social-login-btn google icon-only" onClick={handleGoogle} type="button" aria-label="Sign in with Google">
              <span className="social-login-icon-circle">
                <img src={googleIcon} alt="Google logo" className="social-login-img" />
              </span>
            </button>
            <button className="social-login-btn microsoft icon-only" onClick={handleMicrosoft} type="button" aria-label="Sign in with Microsoft">
              <span className="social-login-icon-circle">
                <img src={microsoftIcon} alt="Microsoft logo" className="social-login-img" />
              </span>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default LoginForm; 