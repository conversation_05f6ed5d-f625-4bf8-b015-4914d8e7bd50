import React from 'react';
import Logo from './Logo';
// import './Footer.css';
import { FaTwitter, FaLinkedin, FaFacebook } from 'react-icons/fa';

const Footer = () => {
    return (
        <footer className="site-footer">
            <div className="container">
                <div className="footer-main">
                    <div className="footer-column about">
                        <Logo color="white" />
                        <p>The all-in-one platform for modern aftermarket operations.</p>
                        <div className="social-links">
                            <a href="#"><FaTwitter /></a>
                            <a href="#"><FaLinkedin /></a>
                            <a href="#"><FaFacebook /></a>
                        </div>
                    </div>

                    <div className="footer-column links">
                        <h4 className="footer-heading">Product</h4>
                        <ul>
                            <li><a href="#modules">Modules</a></li>
                            <li><a href="#industries">Industries</a></li>
                            <li><a href="#pricing">Pricing</a></li>
                            <li><a href="#suites">Suites</a></li>
                        </ul>
                    </div>

                    <div className="footer-column links">
                        <h4 className="footer-heading">Company</h4>
                        <ul>
                            <li><a href="#about">About Us</a></li>
                            <li><a href="#partners">Partners</a></li>
                            <li><a href="#contact">Contact</a></li>
                        </ul>
                    </div>

                    <div className="footer-column contact">
                        <h4 className="footer-heading">Get in Touch</h4>
                        <p>
                            109/1, 20th Main Rd, 5th Block, Rajajinagar,<br />
                            Bengaluru, Karnataka 560010
                        </p>
                        <p>
                            <a href="mailto:<EMAIL>"><EMAIL></a><br/>
                            <a href="tel:+1234567890">+1 (234) 567-890</a>
                        </p>
                    </div>
                </div>
                <div className="footer-bottom">
                    <p>&copy; {new Date().getFullYear()} Disruptive Aftermarket. All rights reserved.</p>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
