import React, { useState } from 'react';
// import './Hero.css';
import Button from '@mui/material/Button';
import hero1 from '../../assets/Images/HeroBanner/hero1.png';
import hero2 from '../../assets/Images/HeroBanner/hero2.png';
import hero7 from '../../assets/Images/HeroBanner/hero7.png';
import hero8 from '../../assets/Images/HeroBanner/hero8.png';
import hero9 from '../../assets/Images/HeroBanner/hero9.png';
import hero10 from '../../assets/Images/HeroBanner/hero10.png';

const heroImages = [hero1, hero2, hero8, hero9, hero7, hero10];

const Hero = () => {
  const [current, setCurrent] = useState(0);

  const handleDotClick = (idx) => {
    setCurrent(idx);
  };

  return (
    <section
      id="hero"
      className="hero-section "
      // style={{
      //   backgroundImage: `url(${heroImages[current]})`,
      //   backgroundSize: 'cover',
      //   backgroundPosition: 'center',
      //   backgroundRepeat: 'no-repeat',
      //   transition: 'background-image 0.5s ease',
      // }}
    >
      <div className="hero-content">
        <h1 className="hero-headline">
          The All-in-One Platform for Modern Aftermarket Operations
        </h1>
        <p className="hero-subheadline">
          From sales and service to inventory and IoT, our 40+ modules provide a complete, integrated solution to streamline your business, boost efficiency, and drive growth.
        </p>
        <div className="hero-cta">
          <Button
            variant="contained"
            color="primary"
            href="#modules"
            className="hero-btn hero-btn-margin"
          >
            Explore Modules
          </Button>
        </div>
      </div>
      <div
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 24,
          display: 'flex',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        {heroImages.map((_, idx) => (
          <span
            key={idx}
            onClick={() => handleDotClick(idx)}
            style={{
              height: 12,
              width: 12,
              margin: '0 6px',
              display: 'inline-block',
              borderRadius: '50%',
              background: current === idx ? '#00bcd4' : '#fff',
              border: '2px solid #00bcd4',
              cursor: 'pointer',
              transition: 'background 0.3s',
            }}
          />
        ))}
      </div>
      <div className="hero-background"></div>
    </section>
  );
};

export default Hero; 