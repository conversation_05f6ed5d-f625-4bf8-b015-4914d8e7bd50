import { modulesData } from './modules';

export const suitesData = [
  {
    name: 'Sales Suite',
    icon: 'point_of_sale',
    category: 'Sales & Service',
    description: 'Everything you need to supercharge your sales and dealer network.',
    modules: ['Sales', 'CPQ', 'Dealer Management', 'Digital Catalogue', 'Ecommerce'],
    cta: 'Request For Demo',
    tags: ['Bestseller', 'Top Rated', 'Mobile Ready', 'OEM Focused'],
    industries: [
      'Component Manufacturers​',
      'Road Machinery',
      'Agriculture Equipment',
      'CNC Machines',
      'Material Handling',
    ],
  },
  {
    name: 'Service Suite',
    category: 'Sales & Service',
    icon: 'support_agent',
    description: 'End-to-end service management for field and workshop operations.',
    modules: ['Service Van', 'Helpdesk', 'Fleet Management', 'Warranty Management', 'Checklist Configurator'],
    cta: 'Request For Demo',
    tags: ['Trending', 'Mobile Ready', 'Included in Suits'],
    industries: [
      'Component Manufacturers​',
      'Healthcare',
      'Mining Equipment',
      'Construction Equipment',
      'Industrial Equipments',
    ],
  },
  {
    name: 'Operations Suite',
    category: 'Operations',
    icon: 'warehouse',
    description: 'Optimize inventory, logistics, and depot management.',
    modules: ['Inventory', 'Warehouse Management', 'Logistics', 'Depot Management', 'Reman'],
    cta: 'Request For Demo',
    tags: ['Best Value', 'Enterprise Ready', 'OEM Focused'],
    industries: [
      'Material Handling',
      'Port Handling',
      'Industrial',
      'Power Generation',
      'Oil and Gas',
    ],
  },
  {
    name: 'Digital Suite',
    category: 'Operations',
    icon: 'sensors',
    description: 'IoT, analytics, and integrations for a connected business.',
    modules: ['IOT', 'Notifications', 'Multi-Brand', 'Interface Management', 'Command Center'],
    cta: 'Request For Demo',
    tags: ['AI-Powered', 'Analytics & Reporting', 'Mobile Ready'],
    industries: [
      'Industrial',
      'Robots​',
      'Power Generation',
      'Healthcare',
      'Manufacturing',
    ],
  },
];

// Add industries to each suite based on its modules (merge with above for demo)
suitesData.forEach(suite => {
  const industriesSet = new Set(suite.industries || []);
  suite.modules.forEach(moduleName => {
    const mod = modulesData.find(m => m.name === moduleName);
    if (mod && Array.isArray(mod.industries)) {
      mod.industries.forEach(ind => industriesSet.add(ind));
    }
  });
  suite.industries = Array.from(industriesSet).filter(Boolean);
}); 