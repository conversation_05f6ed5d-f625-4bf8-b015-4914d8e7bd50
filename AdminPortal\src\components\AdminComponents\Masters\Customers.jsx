import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, ToggleButton, Avatar, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip, Card, ListItemIcon, ToggleButtonGroup,
    TableContainer, CardContent, Divider, Dialog, DialogContent, DialogActions, Grid,
    Tabs, Tab, Accordion, AccordionSummary, AccordionDetails, LinearProgress,
    Stepper, Step, StepLabel
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, <PERSON><PERSON>ff,
    <PERSON><PERSON>hart, <PERSON><PERSON>hart, DonutLarge, <PERSON>tings, Save, Home, NavigateNext, ExpandMore, Check
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';


// --- MOCK DATA & CONFIGS ---
const initialCustomers = [
    { id: 1, name: 'Innovate Corp', shortName: 'Innovate', parentCompany: '', industry: 'Technology', logoName: 'innovate_logo.png', address: '123 Tech Lane, Silicon Valley, CA', companyType: 'Enterprise', currency: 'USD', remarks: 'Key technology partner.', defaultGridSize: 10, isActive: true, companyTheme: 'Default', companyFont: 'Roboto', inventoryFactor: '5', workOrderCushion: '24', orderingCost: '50', status: 'Active', type: 'Enterprise', customerType: 'EE', primaryContact: { name: 'John Doe', email: '<EMAIL>', phone: '************' } },
    { id: 2, name: 'Synergy Ltd', shortName: 'Synergy', parentCompany: 'Innovate Corp', industry: 'Healthcare', logoName: 'synergy_logo.png', address: '456 Health Blvd, Boston, MA', companyType: 'Mid-Market', currency: 'USD', remarks: 'Growing healthcare provider.', defaultGridSize: 12, isActive: true, companyTheme: 'Mint', companyFont: 'Arial', inventoryFactor: '7', workOrderCushion: '48', orderingCost: '35', status: 'Active', type: 'Mid-Market', customerType: 'EN', primaryContact: { name: 'Jane Smith', email: '<EMAIL>', phone: '************' } },
    { id: 3, name: 'Quantum Solutions', shortName: 'Quantum', parentCompany: '', industry: 'Finance', logoName: 'quantum_logo.png', address: '789 Finance Ave, New York, NY', companyType: 'SMB', currency: 'USD', remarks: 'Financial consulting firm.', defaultGridSize: 8, isActive: false, companyTheme: 'Default', companyFont: 'Times New Roman', inventoryFactor: '10', workOrderCushion: '12', orderingCost: '100', status: 'Inactive', type: 'SMB', customerType: 'NN', primaryContact: { name: 'Peter Jones', email: '<EMAIL>', phone: '************' } },
];

const ALL_COLUMNS = [
    { key: 'name', label: 'Name', type: 'string', groupable: true },
    { key: 'status', label: 'Status', type: 'string', groupable: true },
    { key: 'type', label: 'Tier', type: 'string', groupable: true },
    { key: 'industry', label: 'Industry', type: 'string', groupable: true },
    { key: 'customerType', label: 'Customer Type', type: 'string', groupable: true },
    { key: 'primaryContact.name', label: 'Contact Name', type: 'string', groupable: false },
    { key: 'primaryContact.email', label: 'Contact Email', type: 'string', groupable: false },
];

const CUSTOMER_TYPE_LABELS = { EE: 'Existing Existing', EN: 'Existing New', NN: 'Net New' };
const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

// --- MOCK DATA FOR DIALOG TABS ---
const mockBranches = [
    { id: 1, name: 'Branch-Hyderabad', phone: '************', location: 'Levis', email: '<EMAIL>', isHeadOffice: 'Yes', isExternal: 'No', status: 'Active' },
    { id: 2, name: 'Branch-Bangalore', phone: '************', location: 'New Jersey', email: '<EMAIL>', isHeadOffice: 'No', isExternal: 'No', status: 'Inactive' },
];
const mockRelations = [{ id: 1, name: 'TEST456', partnerType: 'Vendor', status: 'Active' }];
const mockBrands = [{ id: 1, name: 'DuraFleet', status: 'Active' }, { id: 2, name: 'Kia Motors', status: 'Active' }];
const mockEmployees = [
    { id: 1, name: 'Admin', code: 'E001', mobile: '************', email: '<EMAIL>', department: 'Administration', designation: 'Credit Agent', status: 'Active' },
    { id: 2, name: 'Employee 11', code: 'A029346', mobile: '************', email: '<EMAIL>', department: 'Service', designation: 'DMS Administrator', status: 'Active' },
];

// --- COLUMN DEFINITIONS FOR DIALOG TABS ---
const branchColumns = [{ key: 'name', label: 'Name' }, { key: 'location', label: 'Location' }, { key: 'phone', label: 'Phone' }, { key: 'email', label: 'Email' }, { key: 'isHeadOffice', label: 'Head Office?' }];
const relationColumns = [{ key: 'name', label: 'Partner/Company' }, { key: 'partnerType', label: 'Partner Type' }];
const brandColumns = [{ key: 'name', label: 'Name' }];
const employeeColumns = [{ key: 'name', label: 'Name' }, { key: 'designation', label: 'Designation' }, { key: 'department', label: 'Department' }, { key: 'email', label: 'Email' }, { key: 'mobile', label: 'Mobile' }];


// --- GENERIC COMPONENTS FOR DIALOG TAB VIEWS ---
const DialogActionButtons = () => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const GenericItemCard = ({ item, columns, isSelected, onSelect }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer><DialogActionButtons /></AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{item.name}</Typography>
            <Typography color="text.secondary" noWrap gutterBottom>{item[columns[1]?.key]}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: item.status }} label={item.status} size="small" />
            <AdminComponents.CardDivider />
            <AdminComponents.CardDetailsGrid>
                {columns.slice(2, 5).map(col => (
                    <React.Fragment key={col.key}>
                        <AdminComponents.CardDetailLabel variant="body2">{col.label}:</AdminComponents.CardDetailLabel>
                        <Typography variant="body2" noWrap>{item[col.key]}</Typography>
                    </React.Fragment>
                ))}
            </AdminComponents.CardDetailsGrid>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const GenericCompactCard = ({ item, columns, isSelected, onSelect }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer><DialogActionButtons /></AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{item.name}</Typography>
                <Typography variant="caption" color="text.secondary">{item[columns[1]?.key]}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{item[columns[2]?.key]}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: item.status }} label={item.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const GenericListItem = ({ item, columns, isSelected, onSelect }) => (
    <AdminComponents.ListItemCard isSelected={isSelected}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isSelected} onChange={() => onSelect(item.id)} onClick={e => e.stopPropagation()} />
            <Box>
                <Typography fontWeight="bold">{item.name}</Typography>
                <Typography variant="body2" color="text.secondary">{item[columns[1]?.key]}</Typography>
            </Box>
            <Typography variant="body2">{item[columns[2]?.key]}</Typography>
            <Typography variant="body2">{item[columns[3]?.key]}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: item.status }} label={item.status} size="small" />
            <AdminComponents.ListItemActions>
                <DialogActionButtons />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);


// --- REUSABLE VIEW COMPONENT FOR DIALOG TABS ---
const DialogTabView = ({ data, columns, entityName }) => {
    const [viewMode, setViewMode] = useState('grid');
    const [selectedIds, setSelectedIds] = useState([]);

    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(data.map((item) => item.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        const selectedIndex = selectedIds.indexOf(id);
        let newSelectedIds = [];
        if (selectedIndex === -1) {
            newSelectedIds = newSelectedIds.concat(selectedIds, id);
        } else {
            newSelectedIds = selectedIds.filter(selectedId => selectedId !== id);
        }
        setSelectedIds(newSelectedIds);
    };

    const renderContent = () => {
        const isItemSelected = (id) => selectedIds.indexOf(id) !== -1;

        switch (viewMode) {
            case 'cards':
                return (
                    <AdminComponents.GridView>
                        {data.map(item => <GenericItemCard key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} />)}
                    </AdminComponents.GridView>
                );
            case 'compact':
                return (
                    <AdminComponents.CompactView>
                        {data.map(item => <GenericCompactCard key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} />)}
                    </AdminComponents.CompactView>
                );
            case 'list':
                return (
                    <AdminComponents.ListView>
                        {data.map(item => <GenericListItem key={item.id} item={item} columns={columns} isSelected={isItemSelected(item.id)} onSelect={handleSelectOne} />)}
                    </AdminComponents.ListView>
                );
            case 'grid':
            default:
                return (
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < data.length} checked={data.length > 0 && selectedIds.length === data.length} onChange={handleSelectAll} /></TableCell>
                                    {columns.map(col => <TableCell key={col.key}>{col.label}</TableCell>)}
                                    <TableCell align="center">Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {data.map(item => {
                                    const isSelected = isItemSelected(item.id);
                                    return (
                                        <TableRow key={item.id} hover role="checkbox" aria-checked={isSelected} tabIndex={-1} selected={isSelected}>
                                            <TableCell padding="checkbox"><Checkbox checked={isSelected} onChange={() => handleSelectOne(item.id)} /></TableCell>
                                            {columns.map(col => <TableCell key={col.key}>{item[col.key] || 'N/A'}</TableCell>)}
                                            <TableCell align="center"><DialogActionButtons /></TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                );
        }
    };

    return (
        <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 2 }}>
                <AdminComponents.StyledToggleButtonGroup
                    size="small"
                    value={viewMode}
                    exclusive
                    onChange={(e, v) => v && setViewMode(v)}
                >
                    <ToggleButton value="cards" title="Card View"><ViewModule /></ToggleButton>
                    <ToggleButton value="compact" title="Compact View"><Apps /></ToggleButton>
                    <ToggleButton value="list" title="List View"><ViewList /></ToggleButton>
                    <ToggleButton value="grid" title="Table View"><GridView /></ToggleButton>
                </AdminComponents.StyledToggleButtonGroup>
                <Box>
                    <Button variant="contained" startIcon={<Add />}>Add {entityName}</Button>
                    {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} sx={{ ml: 1 }}>Delete ({selectedIds.length})</Button>}
                </Box>
            </Box>
            {renderContent()}
        </Box>
    );
};


// --- UI COMPONENTS ---
const TabPanel = (props) => {
    const { children, value, index, ...other } = props;
    return (
        <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`} {...other}>
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
};

const CUSTOMER_STEPS = [
    'Customer',
    'Branch',
    'Company-Company Relation',
    'Brands Association',
    'Employee',
];

// Custom Step Icon Component for the Stepper
function StepIcon(props) {
    const { active, completed, className, icon } = props;
    return (
        <AdminComponents.StepIconRoot ownerState={{ active, completed }} className={className}>
            {completed ? <Check sx={{ color: 'var(--success-main)', fontSize: '1.2rem' }} /> : <span style={{ fontWeight: 600 }}>{icon}</span>}
        </AdminComponents.StepIconRoot>
    );
}

const CustomerDialog = ({ open, onClose, customerData, mode, onSave }) => {
    const [formData, setFormData] = useState({});
    const [tabValue, setTabValue] = useState(0);

    useEffect(() => {
        setFormData(customerData || {});
        setTabValue(0); // Reset to first tab when dialog opens/re-opens
    }, [customerData, open]);

    const isViewOnly = mode === 'view';
    const title = mode === 'add' ? 'Add Customer' : (mode === 'edit' ? 'Edit Customer' : 'View Customer');

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
    };

    const handleSave = () => {
        onSave(formData);
        onClose();
    };

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
            <AdminComponents.DialogHeader>
                <AdminComponents.DialogProgressContainer>
                    <Typography variant="h6">{title}</Typography>
                </AdminComponents.DialogProgressContainer>
                <Box>
                    {!isViewOnly && <Button color="inherit" startIcon={<Save />} onClick={handleSave}>Save</Button>}
                    <Button color="inherit" startIcon={<Close />} onClick={onClose}>Exit</Button>
                </Box>
            </AdminComponents.DialogHeader>

            <Box sx={{ backgroundColor: 'var(--background-secondary)', pt: 2 }}>
                <Box sx={{ px: 3 }}>
                    <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
                        {CUSTOMER_STEPS.map((label, index) => (
                            <Step key={label} completed={tabValue > index}>
                                <StepLabel StepIconComponent={StepIcon}>{""}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>
                </Box>
                <Box>
                    <AdminComponents.StyledTabs
                        value={tabValue}
                        onChange={handleTabChange}
                        variant="fullWidth"
                        aria-label="customer details tabs"
                    >
                        {CUSTOMER_STEPS.map((label) => (
                            <AdminComponents.StyledTab key={label} label={label} />
                        ))}
                    </AdminComponents.StyledTabs>
                </Box>
            </Box>

            <DialogContent sx={{ p: 0, overflow: 'unset' }}>
                <AdminComponents.FixedHeightDialogContent>
                    <TabPanel value={tabValue} index={0}>
                        <Grid container spacing={3}>
                            {/* Row 1 */}
                            <Grid item xs={12} sm={8}><TextField label="Name *" name="name" value={formData.name || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={4}><TextField label="Short Name *" name="shortName" value={formData.shortName || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>

                            {/* Row 2 */}
                            <Grid item xs={12}><TextField label="Address *" name="address" value={formData.address || ''} onChange={handleChange} fullWidth multiline rows={2} disabled={isViewOnly} variant="outlined" /></Grid>

                            {/* Row 3 */}
                            <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly} variant="outlined"><InputLabel>Parent Company</InputLabel><Select native label="Parent Company" value={formData.parentCompany || ''} name="parentCompany" onChange={handleChange}><option aria-label="None" value="" /><option value="Innovate Corp">Innovate Corp</option><option value="Synergy Ltd">Synergy Ltd</option></Select></FormControl></Grid>
                            <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly} variant="outlined"><InputLabel>Company Type *</InputLabel><Select native label="Company Type *" value={formData.companyType || ''} name="companyType" onChange={handleChange}><option aria-label="None" value="" /><option value="Enterprise">Enterprise</option><option value="Mid-Market">Mid-Market</option><option value="SMB">SMB</option></Select></FormControl></Grid>
                            <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly} variant="outlined"><InputLabel>Industry</InputLabel><Select native label="Industry" value={formData.industry || ''} name="industry" onChange={handleChange}><option aria-label="None" value="" /><option value="Technology">Technology</option><option value="Healthcare">Healthcare</option><option value="Finance">Finance</option></Select></FormControl></Grid>

                            {/* Row 4 */}
                            <Grid item xs={12} sm={3}><FormControl fullWidth disabled={isViewOnly} variant="outlined"><InputLabel>Currency *</InputLabel><Select native label="Currency *" value={formData.currency || ''} name="currency" onChange={handleChange}><option aria-label="None" value="" /><option value="USD">USD</option><option value="EUR">EUR</option><option value="GBP">GBP</option></Select></FormControl></Grid>
                            <Grid item xs={12} sm={3}><TextField label="Ordering Cost *" name="orderingCost" value={formData.orderingCost || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={3}><TextField label="Work Order Cushion Hours *" name="workOrderCushion" value={formData.workOrderCushion || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>
                            <Grid item xs={12} sm={3}><TextField label="Inventory Carrying Factor (%) *" name="inventoryFactor" value={formData.inventoryFactor || ''} onChange={handleChange} fullWidth InputProps={{ endAdornment: '%' }} disabled={isViewOnly} variant="outlined" /></Grid>

                            {/* Row 5 */}
                            <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly} variant="outlined"><InputLabel>Company Theme *</InputLabel><Select native label="Company Theme *" value={formData.companyTheme || ''} name="companyTheme" onChange={handleChange}><option aria-label="None" value="" /><option value="Default">Default</option><option value="Mint">Mint</option><option value="Industrial">Industrial</option></Select></FormControl></Grid>
                            <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly} variant="outlined"><InputLabel>Company Font</InputLabel><Select native label="Company Font" value={formData.companyFont || ''} name="companyFont" onChange={handleChange}><option aria-label="None" value="" /><option value="Roboto">Roboto</option><option value="Arial">Arial</option><option value="Verdana">Verdana</option></Select></FormControl></Grid>
                            <Grid item xs={12} sm={4}><TextField label="Logo Name" name="logoName" value={formData.logoName || ''} onChange={handleChange} fullWidth disabled={isViewOnly} variant="outlined" /></Grid>

                            {/* Row 6 */}
                            <Grid item xs={12}><TextField label="Remarks" name="remarks" value={formData.remarks || ''} onChange={handleChange} fullWidth multiline rows={3} disabled={isViewOnly} variant="outlined" /></Grid>

                            {/* Row 7 */}
                            <Grid item xs={12}><FormControlLabel control={<Checkbox name="isActive" checked={formData.isActive || false} onChange={handleChange} disabled={isViewOnly} />} label="Is Active?" /></Grid>
                        </Grid>
                    </TabPanel>
                    <TabPanel value={tabValue} index={1}>
                        <DialogTabView data={mockBranches} columns={branchColumns} entityName="Branch" />
                    </TabPanel>
                    <TabPanel value={tabValue} index={2}>
                        <DialogTabView data={mockRelations} columns={relationColumns} entityName="Relation" />
                    </TabPanel>
                    <TabPanel value={tabValue} index={3}>
                        <DialogTabView data={mockBrands} columns={brandColumns} entityName="Brand" />
                    </TabPanel>
                    <TabPanel value={tabValue} index={4}>
                        <DialogTabView data={mockEmployees} columns={employeeColumns} entityName="Employee" />
                    </TabPanel>
                </AdminComponents.FixedHeightDialogContent>
            </DialogContent>
            <AdminComponents.DialogFooter>
                <AdminComponents.DialogSummary>
                    <Typography variant="body2"><strong>Status:</strong> {formData.status}</Typography>
                    <Typography variant="body2"><strong>Type:</strong> {formData.companyType}</Typography>
                    <Typography variant="body2"><strong>Currency:</strong> {formData.currency}</Typography>
                </AdminComponents.DialogSummary>
                <Box>
                    {!isViewOnly && <Button variant="contained" startIcon={<Save />} onClick={handleSave}>Save</Button>}
                    <Button variant="outlined" startIcon={<Close />} onClick={onClose}>Exit</Button>
                </Box>
            </AdminComponents.DialogFooter>
        </Dialog>
    );
};


const ActionButtons = ({ customer, onView, onEdit, onDelete, isCondensed }) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = (event) => {
        event.stopPropagation();
        setAnchorEl(null);
    };

    const handleAction = (action, event) => {
        handleClose(event);
        action();
    }

    if (isCondensed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton
                    aria-label="more"
                    id="long-button"
                    aria-controls={open ? 'long-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleClick}
                    size="small"
                >
                    <MoreVert />
                </IconButton>
                <Menu
                    id="long-menu"
                    MenuListProps={{
                        'aria-labelledby': 'long-button',
                    }}
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                >
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onView(customer), e)}>
                        <Visibility fontSize="small" /> View
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onEdit(customer), e)}>
                        <Edit fontSize="small" /> Edit
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ErrorMenuItem onClick={(e) => handleAction(() => onDelete([customer.id]), e)}>
                        <Delete fontSize="small" /> Delete
                    </AdminComponents.ErrorMenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(customer)} title="View Details"><Visibility fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onEdit(customer)} title="Edit"><Edit fontSize="small" /></IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([customer.id])} title="Delete"><Delete fontSize="small" /></IconButton>
        </Box>
    );
};

const CustomerCard = ({ customer, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(customer.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ customer, onView, onEdit, onDelete, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{customer.name}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{customer.industry}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: customer.status }} label={customer.status} size="small" />
            <AdminComponents.CardDivider />
            <AdminComponents.CardDetailsGrid>
                <AdminComponents.CardDetailLabel variant="body2">Tier:</AdminComponents.CardDetailLabel>
                <Typography variant="body2" noWrap>{customer.type}</Typography>

                <AdminComponents.CardDetailLabel variant="body2">Type:</AdminComponents.CardDetailLabel>
                <Typography variant="body2" noWrap>{CUSTOMER_TYPE_LABELS[customer.customerType]}</Typography>

                <AdminComponents.CardDetailLabel variant="body2">Contact:</AdminComponents.CardDetailLabel>
                <Typography variant="body2" noWrap>{customer.primaryContact.name}</Typography>
            </AdminComponents.CardDetailsGrid>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const CustomerCompactCard = ({ customer, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(customer.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ customer, onView, onEdit, onDelete, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{customer.name}</Typography>
                <Typography variant="caption" color="text.secondary">{customer.industry}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{customer.type}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: customer.status }} label={customer.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const CustomerListItem = ({ customer, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.ListItemCard isSelected={isSelected}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(customer.id)} onClick={e => e.stopPropagation()} />
            <Box>
                <Typography fontWeight="bold">{customer.name}</Typography>
                <Typography variant="body2" color="text.secondary">{customer.industry}</Typography>
            </Box>
            <Typography variant="body2">{customer.primaryContact.name}</Typography>
            <Typography variant="body2">{customer.type}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: customer.status }} label={customer.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons {...{ customer, onView, onEdit, onDelete, isCondensed }} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const CustomerTable = ({ customers, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, onDelete, onEdit, onView, isCondensed }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (cust, colKey) => {
        if (colKey === 'status') return <AdminComponents.StatusBadge ownerState={{ status: cust.status }} label={cust.status} size="small" />;

        const value = colKey.includes('.') ? colKey.split('.').reduce((o, i) => o?.[i], cust) : cust[colKey];

        if (colKey === 'name') {
            return (
                <AdminComponents.ClickableTypography component="span" onClick={(e) => { e.stopPropagation(); onRowClick(cust); }}>
                    {value}
                </AdminComponents.ClickableTypography>
            );
        }
        return value || '-';
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    const renderGroupedRows = (data, keys, level = 0) => {
        if (!keys.length || !data.length) {
            return data.map(cust => (
                <TableRow key={cust.id} hover selected={selectedId === cust.id}>
                    <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(cust.id)} onChange={() => onSelectOne(cust.id)} /></TableCell>
                    {columnOrder.map(colKey => (
                        <AdminComponents.ContentTableCell key={colKey} onClick={() => onRowClick(cust)}>
                            {renderCellContent(cust, colKey)}
                        </AdminComponents.ContentTableCell>
                    ))}
                    <AdminComponents.ActionTableCell align="center" onClick={() => onRowClick(cust)}>
                        <ActionButtons {...{ customer: cust, onView, onEdit, onDelete, isCondensed }} />
                    </AdminComponents.ActionTableCell>
                </TableRow>
            ));
        }

        const currentKey = keys[0];
        const remainingKeys = keys.slice(1);
        const groupLabel = ALL_COLUMNS.find(c => c.key === currentKey)?.label;

        const grouped = data.reduce((acc, item) => {
            const groupValue = String(item[currentKey] || 'N/A');
            if (!acc[groupValue]) acc[groupValue] = [];
            acc[groupValue].push(item);
            return acc;
        }, {});

        return Object.entries(grouped).map(([groupValue, items]) => (
            <React.Fragment key={`${level}-${groupValue}`}>
                <AdminComponents.GroupHeaderRow>
                    <AdminComponents.GroupHeaderCell
                        colSpan={columnOrder.length + 2}
                        style={{ paddingLeft: theme.spacing(2 + level * 2) }}
                    >
                        <strong>{groupLabel}:</strong> {groupValue} ({items.length})
                    </AdminComponents.GroupHeaderCell>
                </AdminComponents.GroupHeaderRow>
                {renderGroupedRows(items, remainingKeys, level + 1)}
            </React.Fragment>
        ));
    };


    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < customers.length} checked={customers.length > 0 && selectedIds.length === customers.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renderGroupedRows(customers, groupByKeys)}
                </TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

const CustomerGraph = ({ customer, chartType }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && customer) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['EE', 'EN', 'NN'],
                    datasets: [{
                        label: 'Customer Type Engagement (Mock)',
                        data: [Math.random() * 100, Math.random() * 100, Math.random() * 100],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${customer.name} - Engagement Analysis` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [customer, chartType]);

    return (
        <>
            {customer ? (
                <AdminComponents.GraphCanvasContainer>
                    <canvas ref={chartRef}></canvas>
                </AdminComponents.GraphCanvasContainer>
            ) : (
                <AdminComponents.CenteredMessage>
                    <Typography>Select a customer to see graph</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <List disablePadding>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <ListItemText
                            primary={
                                <AdminComponents.ActivityLogTextContainer>
                                    <Typography variant="body2" component="span" color="text.secondary">
                                        <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                        {' '}{log.action}{' '}
                                        {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {log.timestamp}
                                    </Typography>
                                </AdminComponents.ActivityLogTextContainer>
                            }
                        />
                    </AdminComponents.ActivityLogListItem>
                ))}
            </List>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

// --- MAIN APP COMPONENT ---
const Customers = () => {
    const [customers, setCustomers] = useState(initialCustomers);
    const [selectedCustomer, setSelectedCustomer] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, customer: null, mode: 'view' });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new view', target: 'Customer Satisfaction Overview', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Analyst', action: 'Updated a view', target: 'Sales Pipeline Performance', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Marketing Lead', action: 'Deleted a view', target: 'Old Marketing Data', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [summaryFilter, setSummaryFilter] = useState(null); // State for summary card filter
    const [chartType, setChartType] = useState('bar');

    // Advanced Search State
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    const quickFilterOptions = useMemo(() => {
        const industries = [...new Set(customers.map(c => c.industry))];
        const statuses = [...new Set(customers.map(c => c.status))];
        return [...statuses, ...industries];
    }, [customers]);

    const processedCustomers = useMemo(() => {
        let current = customers.filter(c => !c.isDraft);

        // Filter by summary card selection
        if (summaryFilter) {
            current = current.filter(c => c.status === summaryFilter);
        }

        // Simple search term filter
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(c =>
                Object.values(c).some(val =>
                    String(val).toLowerCase().includes(term)
                ) ||
                Object.values(c.primaryContact).some(val =>
                    String(val).toLowerCase().includes(term)
                )
            );
        }

        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(customer => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const customerValue = String(field.includes('.') ? field.split('.').reduce((o, i) => o?.[i], customer) : customer[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();

                    switch (operator) {
                        case 'Equals': return customerValue === filterValue;
                        case 'Not Equals': return customerValue !== filterValue;
                        case 'Contains': return customerValue.includes(filterValue);
                        case 'Starts With': return customerValue.startsWith(filterValue);
                        case 'Ends With': return customerValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }

        // Sorting
        return [...current].sort((a, b) => {
            const valA = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], a) : a[sortColumn];
            const valB = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], b) : b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? (valA || 0) - (valB || 0) : (valB || 0) - (valA || 0);
        });
    }, [customers, searchTerm, activeFilters, sortColumn, sortDirection, summaryFilter]);

    const displayCustomer = useMemo(() => {
        const isSelectedVisible = processedCustomers.some(c => c.id === selectedCustomer?.id);
        if (isSelectedVisible) return selectedCustomer;
        return processedCustomers.length > 0 ? processedCustomers[0] : null;
    }, [processedCustomers, selectedCustomer]);

    const addLog = (logEntry) => {
        const timestamp = new Date().toLocaleString();
        setActivityLog(prev => [{ ...logEntry, timestamp }, ...prev].slice(0, 10));
    };

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const confirmDelete = () => {
        addLog({ user: 'Admin', action: `Deleted ${deleteConfirmation.idsToDelete.length} customer(s).` });
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
    };

    const handleOpenModal = (customer, mode) => {
        const customerData = customer || { name: '', status: 'Active', isActive: true, industry: 'Technology' };
        setModalState({ isOpen: true, customer: customerData, mode });
    };

    const handleCloseModal = () => setModalState({ isOpen: false, customer: null, mode: 'view' });

    const handleSaveCustomer = (customerData) => {
        if (modalState.mode === 'add') {
            const newCustomer = { ...customerData, id: Date.now(), primaryContact: { name: '', email: '' } }; // Add mock contact
            setCustomers(prev => [newCustomer, ...prev]);
            addLog({ user: 'Admin', action: `Added new customer: ${newCustomer.name}` });
        } else {
            setCustomers(prev => prev.map(c => c.id === customerData.id ? { ...c, ...customerData } : c));
            addLog({ user: 'Admin', action: `Edited customer: ${customerData.name}` });
        }
        handleCloseModal();
    };

    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedCustomers.map(c => c.id) : []);
    const handleSelectOne = (id) => {
        const selectedIndex = selectedIds.indexOf(id);
        let newSelectedIds = [];

        if (selectedIndex === -1) {
            newSelectedIds = newSelectedIds.concat(selectedIds, id);
        } else if (selectedIndex === 0) {
            newSelectedIds = newSelectedIds.concat(selectedIds.slice(1));
        } else if (selectedIndex === selectedIds.length - 1) {
            newSelectedIds = newSelectedIds.concat(selectedIds.slice(0, -1));
        } else if (selectedIndex > 0) {
            newSelectedIds = newSelectedIds.concat(
                selectedIds.slice(0, selectedIndex),
                selectedIds.slice(selectedIndex + 1),
            );
        }
        setSelectedIds(newSelectedIds);
    };

    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) {
            setIsGraphVisible(false);
        }
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };

    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;

        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                console.warn("Cannot hide the last column.");
                return;
            }
        } else {
            // Add the column back in its original position
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
        const colLabel = ALL_COLUMNS.find(c => c.key === columnKey)?.label || columnKey;
        addLog({ user: 'Admin', action: `toggled '${colLabel}' column visibility` });
    };

    const handleGraphToggle = () => {
        setIsGraphVisible(prev => !prev);
        setIsSidebarOpen(false);
    };

    const summaryStats = useMemo(() => ({
        total: customers.length,
        active: customers.filter(c => c.status === 'Active').length,
        inactive: customers.filter(c => c.status === 'Inactive').length,
    }), [customers]);

    // --- Advanced Search Handlers ---
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };

    const handleAddQuickFilter = (value) => {
        const field = ['Active', 'Inactive'].includes(value) ? 'status' : 'industry';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };

    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setSummaryFilter(null); // Clear summary filter when applying advanced filters
    };

    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        setSummaryFilter(null); // Clear summary filter on reset
    };

    const handleSummaryCardClick = (status) => {
        setSummaryFilter(prevStatus => (prevStatus === status ? null : status));
        // Clear other filters for a clean summary view
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };

    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isGraphVisible;
        const viewProps = {
            onDelete: handleDeleteRequest,
            onEdit: (customer) => handleOpenModal(customer, 'edit'),
            onView: (customer) => handleOpenModal(customer, 'view'),
        };

        const isSelected = (id) => selectedIds.indexOf(id) !== -1;

        return (
            <AdminComponents.ViewContainer>
                {processedCustomers.length > 0 ? (
                    <>
                        {viewMode === 'cards' && <AdminComponents.GridView>{processedCustomers.map(cust => <CustomerCard key={cust.id} customer={cust} isSelected={displayCustomer?.id === cust.id} onSelect={handleSelectOne} isChecked={isSelected(cust.id)} {...viewProps} isCondensed={false} />)}</AdminComponents.GridView>}
                        {viewMode === 'grid' && (
                            <CustomerTable
                                customers={processedCustomers}
                                onRowClick={setSelectedCustomer}
                                onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                                sortColumn={sortColumn}
                                sortDirection={sortDirection}
                                selectedId={displayCustomer?.id}
                                selectedIds={selectedIds}
                                onSelectAll={handleSelectAll}
                                onSelectOne={handleSelectOne}
                                columnOrder={columnOrder}
                                setColumnOrder={setColumnOrder}
                                addLog={addLog}
                                groupByKeys={groupByKeys}
                                isCondensed={isCondensed}
                                {...viewProps}
                            />
                        )}
                        {viewMode === 'compact' && <AdminComponents.CompactView>{processedCustomers.map(cust => <CustomerCompactCard key={cust.id} customer={cust} isSelected={displayCustomer?.id === cust.id} onSelect={handleSelectOne} isChecked={isSelected(cust.id)} {...viewProps} isCondensed={false} />)}</AdminComponents.CompactView>}
                        {viewMode === 'list' && <AdminComponents.ListView>{processedCustomers.map(cust => <CustomerListItem key={cust.id} customer={cust} isSelected={displayCustomer?.id === cust.id} onSelect={handleSelectOne} isChecked={isSelected(cust.id)} {...viewProps} isCondensed={isCondensed} />)}</AdminComponents.ListView>}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching Customers</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === null} onClick={() => handleSummaryCardClick(null)}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Customers</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === 'Active'} onClick={() => handleSummaryCardClick('Active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === 'Inactive'} onClick={() => handleSummaryCardClick('Inactive')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.inactive}</Typography><Typography variant="body2">Inactive</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenModal(null, 'add')}>Add Customer</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedCustomers.length > 0 && selectedIds.length === processedCustomers.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedCustomers.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<Settings />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth>
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <CustomerGraph customer={displayCustomer} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={isSidebarOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">
                                {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                            </Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}>
                                <Close />
                            </IconButton>
                        </AdminComponents.SidebarHeader>

                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.QuickFilterContainer>
                                            {quickFilterOptions.map(opt => (
                                                <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                            ))}
                                        </AdminComponents.QuickFilterContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Field</InputLabel>
                                            <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Operator</InputLabel>
                                            <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                                        <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {activeFilters.length > 0 ? activeFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnActionContainer>
                                            <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                            <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                        </AdminComponents.ColumnActionContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                <Chip
                                                    key={key}
                                                    label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                    onDelete={() => handleGroupByChange(key)}
                                                />
                                            )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={
                                                        <Checkbox
                                                            checked={groupByKeys.includes(col.key)}
                                                            onChange={() => handleGroupByChange(col.key)}
                                                        />
                                                    }
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                        </AdminComponents.SidebarContent>

                        <AdminComponents.SidebarFooter>
                            {sidebarMode === 'search' && (
                                <>
                                    <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                    <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                            )}
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>

                <CustomerDialog
                    open={modalState.isOpen}
                    onClose={handleCloseModal}
                    customerData={modalState.customer}
                    mode={modalState.mode}
                    onSave={handleSaveCustomer}
                />

            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Customers;