import React from 'react';
import { partnersData } from '../../data/partners';
// import './Partners.css';
import { FaQuoteLeft } from 'react-icons/fa';

const handleImgError = (e, fallback) => {
  if (fallback) {
    e.target.onerror = null;
    e.target.src = fallback;
  }
};

const Partners = () => (
  <section id="partners" className="partners-section">
    <div className="container">
      <div className="section-header">
        <h2 className="section-title">
          <span role="img" aria-label="handshake">🤝</span> Our Trusted Partners
        </h2>
        <p className="section-subtitle">
          Leading global brands trust our platform to power their aftermarket business.
        </p>
      </div>
      <div className="partners-grid">
        {[...partnersData].sort((a, b) => a.name.localeCompare(b.name)).map((p) => (
          <div className="partner-logo-wrap" key={p.name} title={p.name}>
            <img
              src={p.logo}
              alt={p.name}
              className="partner-logo"
              loading="lazy"
              onError={e => handleImgError(e, p.fallback)}
            />
            {p.testimonial ? (
              <div className="partner-testimonial">
                <FaQuoteLeft className="testimonial-quote-icon" />
                <div className="testimonial-quote">{p.testimonial.quote}</div>
                <div className="testimonial-author">{p.testimonial.name}</div>
                <div className="testimonial-title">{p.testimonial.title}</div>
              </div>
            ) : (
              <span className="partner-tooltip">{p.name}</span>
            )}
          </div>
        ))}
      </div>
    </div>
  </section>
);

export default Partners; 