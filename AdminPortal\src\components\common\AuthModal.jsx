import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// import './AuthModal.css';
import SignUpForm from './SignUpForm';
import LoginForm from './LoginForm';
import Logo from '../layout/CommerceLayout/Logo';

const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
};

const cardVariants = {
    hidden: { opacity: 0, scale: 0.92, y: 40 },
    visible: { opacity: 1, scale: 1, y: 0, transition: { type: 'spring', stiffness: 340, damping: 28 } },
    exit: { opacity: 0, scale: 0.92, y: 40 }
};

const tabs = [
  { key: 'user', label: 'User' },
  { key: 'admin', label: 'Admin' }
];

const AuthModal = ({ mode = 'login', onClose, onLogin }) => {
  const [activeTab, setActiveTab] = useState('user');
  const [authMode, setAuthMode] = useState(mode); // 'login' or 'signup'

  // Reset to login when switching tabs
  const handleTabSwitch = (tab) => {
    setActiveTab(tab);
    setAuthMode('login');
  };

  return (
    <AnimatePresence>
      <motion.div
        className="auth-modal-overlay"
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        onClick={onClose}
        key="overlay"
      >
        <motion.div
          className="auth-modal-card"
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          onClick={e => e.stopPropagation()}
          key="card"
        >
          <button className="auth-modal-close" onClick={onClose} aria-label="Close">
            <span className="auth-modal-close-bg">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" stroke="#0096a7" strokeWidth="2.4" strokeLinecap="round" strokeLinejoin="round">
                <line x1="20" y1="8" x2="8" y2="20"/>
                <line x1="8" y1="8" x2="20" y2="20"/>
              </svg>
            </span>
          </button>
          <div className="auth-modal-2col">
            <div className="auth-modal-2col-inner">
              <div className="auth-modal-2col-left">
                <div className="auth-modal-branding">
                  <Logo className="logo" align="center" />
                  <h3 className="auth-modal-headline">Experience the Future of Aftermarket Access</h3>
                  <ul className="auth-modal-values">
                    <li> Enterprise-grade security for every user</li>
                    <li> Effortless onboarding and sign-in</li>
                    <li> Designed for both Users & Admins</li>
                    <li> Fast, intuitive, and always available</li>
                  </ul>
                </div>
              </div>
              <div className="auth-modal-2col-right">
                <div className="auth-modal-content">
                  <div className="auth-modal-tabs">
                    {tabs.map((tab, idx) => (
                      <button
                        key={tab.key}
                        className={`auth-modal-tab${activeTab === tab.key ? ' active' : ''}`}
                        onClick={() => handleTabSwitch(tab.key)}
                      >
                        {tab.label}
                        {activeTab === tab.key && (
                          <motion.div
                            className="auth-modal-tab-underline"
                            layoutId="auth-tab-underline"
                            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                          />
                        )}
                      </button>
                    ))}
                  </div>
                  <div className="auth-modal-tab-content-wrapper">
                    <div className="auth-modal-content-inner">
                      <AnimatePresence mode="wait" initial={false}>
                        {activeTab === 'user' && (
                          <motion.div
                            key={authMode}
                            className="auth-modal-tab-content auth-modal-absolute-content"
                            initial={authMode === 'signup' ? { opacity: 0, x: 80 } : { opacity: 0, x: -80 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={authMode === 'signup' ? { opacity: 0, x: -80 } : { opacity: 0, x: 80 }}
                            transition={{ duration: 0.38, ease: 'easeInOut' }}
                          >
                            {authMode === 'signup' ? (
                              <>
                                <SignUpForm
                                  onSuccess={onLogin}
                                  onCancel={() => setAuthMode('login')}
                                />
                                <div className="auth-modal-switch-link">
                                  Already have an account?{' '}
                                  <span onClick={() => setAuthMode('login')} tabIndex={0} role="button">Log In</span>
                                </div>
                              </>
                            ) : (
                              <>
                                <LoginForm
                                  onSuccess={userObj => onLogin && onLogin(userObj)}
                                  onCancel={onClose}
                                  userType="user"
                                />
                                <div className="auth-modal-switch-link">
                                  Don't have an account?{' '}
                                  <span onClick={() => setAuthMode('signup')} tabIndex={0} role="button">Sign Up</span>
                                </div>
                              </>
                            )}
                          </motion.div>
                        )}
                        {activeTab === 'admin' && (
                          <motion.div
                            key="admin"
                            className="auth-modal-tab-content"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -20 }}
                            transition={{ duration: 0.22 }}
                          >
                            <LoginForm
                              onSuccess={userObj => onLogin && onLogin(userObj)}
                              onCancel={onClose}
                              userType="admin"
                            />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AuthModal; 