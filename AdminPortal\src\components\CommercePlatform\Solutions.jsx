import React, { useState, useMemo, useEffect } from 'react';
import { suitesData } from '../../data/suites';
import { modulesData } from '../../data/modules';
import DynamicIcon from '../common/DynamicIcon';
import SidebarAdvancedFilter from './SidebarAdvancedFilter';
import ModuleCard from './ModuleCard';
import { MdFilterList, MdFilterAlt } from 'react-icons/md';
// import './Suites.css';
// import './Modules.css';
// import './SidebarAdvancedFilter.css';
import Button from '@mui/material/Button';
import { industryIconList } from '../../data/industryIconMap';

// Dynamically generate unique tags (specialViews) and categories (functionalAreas)
const getUniqueTags = () => {
  const tags = [
    ...modulesData.flatMap(m => m.tags || []),
    ...suitesData.flatMap(s => s.tags || [])
  ];
  const unique = Array.from(new Set(tags.map(t => t.trim()))).filter(Boolean);
  return ['All', ...unique.sort((a, b) => a.localeCompare(b))];
};

const getUniqueCategories = () => {
  const categories = [
    ...modulesData.map(m => m.category).filter(Boolean),
    ...suitesData.flatMap(s => (s.modules || []).map(modName => {
      const mod = modulesData.find(m => m.name === modName);
      return mod && mod.category;
    })).filter(Boolean)
  ];
  return Array.from(new Set(categories.map(c => c.trim()))).filter(Boolean).sort((a, b) => a.localeCompare(b));
};

const moduleHeadings = {
  'Sales': {
    title: 'Sales',
    desc: 'Manage sales pipelines, quotes, and orders efficiently.'
  },
  'CPQ': {
    title: 'CPQ',
    desc: 'Configure, price, and quote complex products with ease.'
  },
  'Digital Catalogue': {
    title: 'Digital Catalogue',
    desc: 'Provide interactive and always up-to-date product catalogs.'
  },
  'Ecommerce': {
    title: 'Ecommerce',
    desc: 'Launch a powerful B2B/B2C online store integrated with your backend.'
  },
  'Fleet Management': {
    title: 'Fleet Management',
    desc: 'Monitor and manage vehicle fleets for service and logistics.'
  },
  'Rental': {
    title: 'Rental',
    desc: 'Manage rental equipment, from contracts to billing and availability.'
  },
  'Reman': {
    title: 'Reman',
    desc: 'Oversee the entire remanufacturing lifecycle, from core return to final assembly.'
  },
  'Warehouse Management': {
    title: 'Warehouse Management',
    desc: 'Optimize receiving, put-away, picking, and shipping operations.'
  },
  'Logistics': {
    title: 'Logistics',
    desc: 'Manage transportation, shipping, and delivery of parts and products.'
  },
  'Site Operations': {
    title: 'Site Operations',
    desc: 'Manage day-to-day operations for physical plant and factory sites.'
  },
  'PDI (Pre-Delivery Inspection)': {
    title: 'PDI (Pre-Delivery Inspection)',
    desc: 'Standardize and track pre-delivery inspections for quality assurance.'
  },
  'Command Center': {
    title: 'Command Center',
    desc: 'Get a high-level overview of all operations from a central dashboard.'
  },
  'Helpdesk': {
    title: 'Helpdesk',
    desc: 'Internal ticketing and support system for employees.'
  },
  'Special Tools': {
    title: 'Special Tools',
    desc: 'Manage high-value, specialized tools with detailed tracking.'
  },
  'Warranty Management': {
    title: 'Warranty Management',
    desc: 'Handle warranty claims, entitlements, and supplier recovery.'
  },
  'Contract Management': {
    title: 'Contract Management',
    desc: 'Manage customer service contracts, SLAs, and entitlements.'
  },
  'Checklist Configurator': {
    title: 'Checklist Configurator',
    desc: 'Create dynamic checklists for inspections, service, and safety.'
  },
  'Payment': {
    title: 'Payment',
    desc: 'Integrate with payment gateways for seamless transactions.'
  },
  'Dealer Audit': {
    title: 'Dealer Audit',
    desc: 'Conduct and track audits of dealer operations and compliance.'
  },
};

const Solutions = ({ handleAddToCart, selectedIndustry, onClearIndustry }) => {
  const [activeTab, setActiveTab] = useState('suites'); // 'suites' or 'modules'
  const [search, setSearch] = useState('');
  const [selectedSpecialView, setSelectedSpecialView] = useState('All');
  const [selectedFunctionalArea, setSelectedFunctionalArea] = useState('');
  const [selectedIndustries, setSelectedIndustries] = useState([]);
  const [solutionType, setSolutionType] = useState('all');
  const [showAll, setShowAll] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 900);

  // Dynamically generated filter options
  const specialViews = useMemo(getUniqueTags, []);
  const functionalAreas = useMemo(getUniqueCategories, []);

  // Responsive: close sidebar on filter apply (mobile)
  const handleFilterApply = () => {
    if (window.innerWidth <= 900) setSidebarOpen(false);
  };

  // Filtering logic
  const filterByTags = (item, selectedTag) => {
    if (!selectedTag || selectedTag === 'All') return true;
    return item.tags && item.tags.map(t => t.toLowerCase()).includes(selectedTag.toLowerCase());
  };

  const filterByFunctionalArea = (item, selectedArea) => {
    if (!selectedArea) return true;
    // For modules: category; for suites: any module's category
    if (item.category) return item.category === selectedArea;
    if (item.modules) {
      // Find if any included module matches the selected area
      return item.modules.some(modName => {
        const mod = modulesData.find(m => m.name === modName);
        return mod && mod.category === selectedArea;
      });
    }
    return true;
  };

  // For industries, if you add industries field, add similar logic here
  const filterByIndustries = (item, selectedIndustries) => {
    if (!selectedIndustries || selectedIndustries.length === 0) return true;
    if (item.industries) {
      return selectedIndustries.some(ind => item.industries.includes(ind));
    }
    // For suites, check if any included module matches
    if (item.modules) {
      return item.modules.some(modName => {
        const mod = modulesData.find(m => m.name === modName);
        return mod && mod.industries && selectedIndustries.some(ind => mod.industries.includes(ind));
      });
    }
    return false;
  };

  // Solution type: 'all', 'bundles', 'modules'
  const showSuites = solutionType === 'all' || solutionType === 'bundles';
  const showModules = solutionType === 'all' || solutionType === 'modules';

  const filteredSuites = useMemo(() => {
    if (!showSuites) return [];
    return suitesData.filter(suite =>
      filterByTags(suite, selectedSpecialView) &&
      filterByFunctionalArea(suite, selectedFunctionalArea) &&
      filterByIndustries(suite, selectedIndustries)
    ).sort((a, b) => a.name.localeCompare(b.name));
  }, [selectedSpecialView, selectedFunctionalArea, selectedIndustries, showSuites]);

  const filteredModules = useMemo(() => {
    if (!showModules) return [];
    return modulesData.filter(module =>
      filterByTags(module, selectedSpecialView) &&
      filterByFunctionalArea(module, selectedFunctionalArea) &&
      filterByIndustries(module, selectedIndustries)
    ).sort((a, b) => a.name.localeCompare(b.name));
  }, [selectedSpecialView, selectedFunctionalArea, selectedIndustries, showModules]);

  // Tab-specific data
  const isSuites = activeTab === 'suites';
  const gridData = isSuites ? filteredSuites : filteredModules;
  const maxColumns = isSuites ? 3 : 4;
  const rowsToShow = 2;
  const maxToShow = maxColumns * rowsToShow;
  const visibleData = showAll ? gridData : gridData.slice(0, maxToShow);

  // Clear all filters
  const clearFilters = () => {
    setSearch('');
    setSelectedSpecialView('All');
    setSelectedFunctionalArea('');
    setSelectedIndustries([]);
    setSolutionType('all');
    if (onClearIndustry) onClearIndustry();
  };

  // Responsive sidebar toggle
  const handleSidebarToggle = () => setSidebarOpen(open => !open);

  useEffect(() => {
    const handler = (e) => {
      if (e.detail && (e.detail.tab === 'suites' || e.detail.tab === 'modules')) {
        setActiveTab(e.detail.tab);
        setShowAll(false);
      }
    };
    window.addEventListener('solutionsTabSwitch', handler);
    return () => window.removeEventListener('solutionsTabSwitch', handler);
  }, []);

  // Watch for solutionType changes and switch tab accordingly
  useEffect(() => {
    if (solutionType === 'modules') {
      setActiveTab('modules');
      setShowAll(false);
    } else if (solutionType === 'bundles') {
      setActiveTab('suites');
      setShowAll(false);
    }
  }, [solutionType]);

  // Watch for selectedIndustry changes and update state
  useEffect(() => {
    if (selectedIndustry) {
      // Check if any suites are available for this industry
      const suitesForIndustry = suitesData.filter(suite => suite.industries && suite.industries.includes(selectedIndustry));
      if (suitesForIndustry.length > 0) {
        setActiveTab('suites');
        setSelectedIndustries([selectedIndustry]);
        setShowAll(true);
      } else {
        setActiveTab('modules');
        setSelectedIndustries([selectedIndustry]);
        setShowAll(true);
      }
    }
  }, [selectedIndustry]);

  // Responsive: grid expands when sidebar is hidden
  const gridClass = `${isSuites ? 'suites-grid' : 'modules-grid'}${!sidebarOpen ? ' grid-expanded' : ''}`;

  // Find the selected industry object
  const selectedIndustryObj = selectedIndustry ? industryIconList.find(i => i.name === selectedIndustry) : null;

  // Section headings and descriptions
  const suitesHeading = selectedIndustryObj ? selectedIndustryObj.name : 'Featured OEM Solutions & Bundles';
  const suitesDesc = selectedIndustryObj ? selectedIndustryObj.punchline : 'Accelerate your Aftermarket Digital Transformation with our pre-configured, value-packed suites, designed specifically for OEM and manufacturer needs.';
  const modulesHeading = selectedIndustryObj ? selectedIndustryObj.name : 'One Platform, Endless Capabilities';
  const modulesDesc = selectedIndustryObj ? selectedIndustryObj.punchline : 'Discover the integrated modules that power the AfterMarket Cloud.';
  const modulesSubDesc = selectedIndustryObj ? selectedIndustryObj.punchline : '';
  const solutionsHeading = 'Solutions';
  const solutionsDesc = 'Explore our full suite of modular SaaS solutions for every aftermarket need.';

  return (
    <section id="solutions" className="solutions-section">
      <div className="solutions-centered-container">
        <div className="section-header centered section-header-margin-top">
          <h2 className="section-title">{isSuites ? suitesHeading : modulesHeading}</h2>
          <p className="section-subtitle">{isSuites ? suitesDesc : modulesDesc}</p>
        </div>
        <div className="solutions-tabs-row modern-toggle">
          <div className="toggle-group">
            <button
              className={`toggle-btn${isSuites ? ' active' : ''}`}
              onClick={() => { setActiveTab('suites'); setShowAll(false); }}
            >
              Suites
            </button>
            <button
              className={`toggle-btn${!isSuites ? ' active' : ''}`}
              onClick={() => { setActiveTab('modules'); setShowAll(false); }}
            >
              Modules
            </button>
          </div>
          <button className="sidebar-toggle-btn" onClick={handleSidebarToggle}>
            {sidebarOpen ? <MdFilterAlt className="sidebar-toggle-icon" /> : <MdFilterList className="sidebar-toggle-icon" />}
            {sidebarOpen ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>
      </div>
      <div className="solutions-main-flex">
        {sidebarOpen && (
          <SidebarAdvancedFilter
            open={sidebarOpen}
            onClose={() => setSidebarOpen(false)}
            search={search}
            setSearch={setSearch}
            categories={[]}
            selectedCategory={''}
            setSelectedCategory={() => {}}
            specialViews={specialViews}
            selectedSpecialView={selectedSpecialView}
            setSelectedSpecialView={setSelectedSpecialView}
            functionalAreas={functionalAreas}
            selectedFunctionalArea={selectedFunctionalArea}
            setSelectedFunctionalArea={setSelectedFunctionalArea}
            industries={industryIconList.map(i => i.name).sort((a, b) => a.localeCompare(b))}
            selectedIndustries={selectedIndustries}
            setSelectedIndustries={setSelectedIndustries}
            solutionType={solutionType}
            setSolutionType={setSolutionType}
            onClearAll={clearFilters}
            resultCount={gridData.length}
            onFilterApply={handleFilterApply}
          />
        )}
        <div className={`solutions-content${showAll ? ' scrollable' : ''}`}>
          <div className={gridClass}>
            {isSuites
              ? visibleData.map((suite, idx) => {
                  // Add more data to card-description based on suite name
                  let extraDesc = '';
                  if (suite.name.toLowerCase().includes('sales')) {
                    extraDesc = 'Empower your sales team with advanced CRM and analytics.';
                  } else if (suite.name.toLowerCase().includes('service')) {
                    extraDesc = 'Boost service efficiency with real-time tracking and automation.';
                  } else if (suite.name.toLowerCase().includes('operations')) {
                    extraDesc = 'Seamlessly manage inventory, logistics, and depot operations.';
                  } else if (suite.name.toLowerCase().includes('digital')) {
                    extraDesc = 'Connect your business with IoT, analytics, and integrations.';
                  }
                  return (
                    <div className="suite-card module-card left-align" key={suite.name}>
                      {suite.tags && suite.tags.length > 0 && (
                        <div className={`module-tag-badge tag-${suite.tags[0].toLowerCase().replace(/\s+/g, '-')}`}>{suite.tags[0]}</div>
                      )}
                      <div className="card-header">
                        <div className="card-icon-container">
                          <DynamicIcon name={suite.icon} />
                        </div>
                        <div className="suite-card-gap" /> {/* gap between icon and title */}
                        <div className="card-title-container">
                          <h4 className="card-title">{suite.name}</h4>
                          {suite.category && <p className="card-category">{suite.category}</p>}
                        </div>
                      </div>
                      <p className="card-description">
                        {suite.description}
                        {extraDesc && <><br />{extraDesc}</>}
                      </p>
                      {suite.modules && suite.modules.length > 0 && (
                        <div className="suite-includes">
                          <span className="suite-includes-label"><b>Includes:</b></span> {suite.modules.join(', ')}
                        </div>
                      )}
                      <div className="card-footer">
                        <div className="card-actions">
                          <Button variant="contained" color="primary" size="small" sx={{ borderRadius: 50, fontWeight: 700, mr: 1 }} className="solutions-btn solutions-btn-margin">
                            View Details
                          </Button>
                          <Button variant="outlined" color="primary" size="small" onClick={() => handleAddToCart(suite, 'suite')} sx={{ borderRadius: 50, fontWeight: 700 }} className="solutions-btn">
                            Add to Cart
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })
              : visibleData.map(module => {
                  const heading = moduleHeadings[module.name];
                  return (
                    <div className="module-card" key={module.id}>
                      {module.tags && module.tags.length > 0 && (
                        <div className={`module-tag-badge tag-${module.tags[0].toLowerCase().replace(/\s+/g, '-')}`}>{module.tags[0]}</div>
                      )}
                      <div className="card-header">
                        <div className="card-icon-container">
                          <DynamicIcon name={module.icon} />
                        </div>
                        <div className="card-title-container">
                          <h4 className="card-title">{heading ? heading.title : module.name}</h4>
                          <p className="card-category">{module.category}</p>
                        </div>
                      </div>
                      <p className="card-description">{heading ? heading.desc : module.description}</p>
                      <div className="card-footer">
                        <div className="card-actions">
                          <Button variant="contained" color="primary" size="small" sx={{ borderRadius: 50, fontWeight: 700, mr: 1 }} className="solutions-btn solutions-btn-margin">
                            Request For Demo
                          </Button>
                          <Button variant="outlined" color="primary" size="small" onClick={() => handleAddToCart(module, 'module')} sx={{ borderRadius: 50, fontWeight: 700 }} className="solutions-btn">
                            Add to Cart
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
          </div>
          {gridData.length > maxToShow && (
            <div className="view-more-row">
              <Button variant="contained" color="primary" onClick={() => setShowAll(v => !v)} className="solutions-btn solutions-btn-margin">
                {showAll ? 'View Less' : 'View More'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Solutions; 