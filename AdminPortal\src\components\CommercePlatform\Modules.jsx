import React, { useState, useMemo } from 'react';
import { modulesData } from '../../data/modules';
import { industryIconList } from '../../data/industryIconMap';
import ModuleCard from './ModuleCard';
import AdvancedFilter from './FilterSidebar';
// import './Modules.css';

const allFeatures = Array.from(new Set(modulesData.map(m => m.category)));

const Modules = ({ handleAddToCart }) => {
    const [search, setSearch] = useState('');
    const [selectedFeatures, setSelectedFeatures] = useState([]);
    const [selectedIndustries, setSelectedIndustries] = useState([]);
    const [showAll, setShowAll] = useState(false);

    // Filtering logic
    const filteredModules = useMemo(() => {
        return [...modulesData].sort((a, b) => a.name.localeCompare(b.name)).filter(m => {
            // Search
            if (search && !m.name.toLowerCase().includes(search.toLowerCase()) && !m.description.toLowerCase().includes(search.toLowerCase())) {
                return false;
            }
            // Features (category)
            if (selectedFeatures.length > 0 && !selectedFeatures.includes(m.category)) {
                return false;
            }
            // Industries (simulate: show all if none selected)
            // (If you want to map modules to industries, add an 'industries' field to each module)
            return true;
        });
    }, [search, selectedFeatures, selectedIndustries]);

    return (
        <section id="modules" className="modules-section">
            <div className="modules-main">
                <div className="modules-header-row">
                    <div className="section-header">
                        <h2 className="section-title">One Platform, Endless Capabilities</h2>
                        <p className="section-subtitle">
                            Discover the integrated modules that power the AfterMarket Cloud.
                        </p>
                    </div>
                </div>
                <AdvancedFilter
                    search={search}
                    setSearch={setSearch}
                    features={allFeatures}
                    selectedFeatures={selectedFeatures}
                    setSelectedFeatures={setSelectedFeatures}
                    industries={industryIconList.map(i => i.name)}
                    selectedIndustries={selectedIndustries}
                    setSelectedIndustries={setSelectedIndustries}
                />
                <div className="grid-center-wrapper">
                  <div className={`modules-grid${filteredModules.length === 1 ? ' single-card' : ''} ${showAll ? 'expanded' : ''}`}>
                    {filteredModules.map(module => (
                        <ModuleCard key={module.id} module={module} handleAddToCart={handleAddToCart} />
                    ))}
                  </div>
                </div>
                {filteredModules.length > 0 && (
                    <div className="view-more-row">
                        <button className="view-more-btn" onClick={() => setShowAll(v => !v)}>
                            {showAll ? 'View Less' : 'View More'}
                        </button>
                    </div>
                )}
            </div>
        </section>
    );
};

export default Modules; 