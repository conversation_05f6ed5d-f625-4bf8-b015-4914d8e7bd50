import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppBar, Toolbar, IconButton, Avatar, Box, Tooltip, Badge, Menu, MenuItem, InputBase, Divider, Typography, List, ListItem, ListItemText, ListItemIcon, Button, Modal, Paper, Grid, Popover, ListSubheader } from '@mui/material';
import {
    Search as SearchIcon,
    Language as LanguageIcon,
    MonetizationOnOutlined as CurrencyIcon,
    Brightness4 as DarkModeIcon,
    Brightness7 as LightModeIcon,
    ViewQuiltOutlined as LayoutIcon,
    HelpOutline as HelpIcon,
    Logout as LogoutIcon,
    ArticleOutlined as ReleaseNotesIcon,
    MenuBookOutlined as UserManualIcon,
    NotificationsNoneOutlined,
    SettingsOutlined as SettingsIcon,
    Apps as AppsIcon,
    ArrowBackIosNew as BackIcon,
    Check,
    ContactSupportOutlined,
    DescriptionOutlined,
    DnsOutlined,
    Headset<PERSON>icOutlined,
    DragIndicator,
    SaveOutlined
} from '@mui/icons-material';
import Logo from '../CommerceLayout/Logo';
import { AdminComponents } from '../../../styles/theme';
import userIcon from '../../../assets/Images/social-icons/user.png';

// --- Draggable List Item for Config Modal ---
const DraggableListItem = ({ item, ...props }) => (
    <AdminComponents.DraggableListItemStyled {...props}>
        <AdminComponents.DraggableListItemIcon><DragIndicator /></AdminComponents.DraggableListItemIcon>
        <AdminComponents.ListItemIconStyled>{item.icon}</AdminComponents.ListItemIconStyled>
        <ListItemText primary={item.name} />
    </AdminComponents.DraggableListItemStyled>
);

// --- Configuration Modal for Top Bar Icons ---
const ConfigureTopBarModal = ({ open, onClose, config, onSave, allItems }) => {
    const [visible, setVisible] = useState([]);
    const [inMenu, setInMenu] = useState([]);
    const draggedItemRef = useRef(null);
    const dragOverItemRef = useRef(null);

    useEffect(() => {
        if (open) {
            const allItemsMap = new Map(allItems.map(i => [i.id, i]));
            setVisible(config.visible.map(id => allItemsMap.get(id)).filter(Boolean));
            setInMenu(config.inMenu.map(id => allItemsMap.get(id)).filter(Boolean));
        }
    }, [open, config, allItems]);

    const handleDragStart = (e, position, list) => {
        draggedItemRef.current = { item: list === 'visible' ? visible[position] : inMenu[position], position, list };
    };

    const handleDragEnter = (e, position, list) => {
        dragOverItemRef.current = { position, list };
    };
    
    const handleDrop = () => {
        if (!draggedItemRef.current) return;
        const source = draggedItemRef.current;
        const target = dragOverItemRef.current;
        let newVisible = [...visible];
        let newInMenu = [...inMenu];
        if (source.list === 'visible') newVisible.splice(source.position, 1);
        else newInMenu.splice(source.position, 1);
        if (!target) {
             if (source.list === 'inMenu') newVisible.push(source.item);
             else newInMenu.push(source.item);
        } else {
            if (target.list === 'visible') newVisible.splice(target.position, 0, source.item);
            else newInMenu.splice(target.position, 0, source.item);
        }
        setVisible(newVisible);
        setInMenu(newInMenu);
        draggedItemRef.current = null;
        dragOverItemRef.current = null;
    };

    const handleSave = () => {
        onSave({ visible: visible.map(i => i.id), inMenu: inMenu.map(i => i.id) });
        onClose();
    };

    return (
        <Modal open={open} onClose={onClose}>
            <AdminComponents.ConfigureTopBarModalPaper>
                <AdminComponents.ConfigureTopBarModalTitle variant="h5">Customize Top Bar</AdminComponents.ConfigureTopBarModalTitle>
                <AdminComponents.ConfigureTopBarModalSubtitle variant="body2" color="text.secondary">Drag icons between lists to pin them to the top bar or hide them in the Apps Menu.</AdminComponents.ConfigureTopBarModalSubtitle>
                <Grid container spacing={2} onDrop={handleDrop} onDragOver={(e) => e.preventDefault()}>
                    <Grid xs={6}>
                        <AdminComponents.ConfigureTopBarModalListTitle variant="subtitle2">Visible on Top Bar</AdminComponents.ConfigureTopBarModalListTitle>
                        <AdminComponents.ConfigureTopBarModalListPaper variant="outlined" onDragEnter={(e) => handleDragEnter(e, visible.length, 'visible')}>
                            <List>{visible.map((item, index) => (<Box key={item.id} onDragEnter={(e) => handleDragEnter(e, index, 'visible')}><DraggableListItem item={item} draggable onDragStart={(e) => handleDragStart(e, index, 'visible')} /></Box>))}</List>
                        </AdminComponents.ConfigureTopBarModalListPaper>
                    </Grid>
                    <Grid xs={6}>
                        <AdminComponents.ConfigureTopBarModalListTitle variant="subtitle2">Hidden in Apps Menu</AdminComponents.ConfigureTopBarModalListTitle>
                        <AdminComponents.ConfigureTopBarModalListPaper variant="outlined" onDragEnter={(e) => handleDragEnter(e, inMenu.length, 'inMenu')}>
                            <List>{inMenu.map((item, index) => (<Box key={item.id} onDragEnter={(e) => handleDragEnter(e, index, 'inMenu')}><DraggableListItem item={item} draggable onDragStart={(e) => handleDragStart(e, index, 'inMenu')} /></Box>))}</List>
                        </AdminComponents.ConfigureTopBarModalListPaper>
                    </Grid>
                </Grid>
                <AdminComponents.ConfigureTopBarModalActions>
                    <Button variant="text" onClick={onClose}>Cancel</Button>
                    <Button variant="contained" onClick={handleSave} startIcon={<SaveOutlined />}>Save Layout</Button>
                </AdminComponents.ConfigureTopBarModalActions>
            </AdminComponents.ConfigureTopBarModalPaper>
        </Modal>
    );
};


const TopBar = ({ logout, layoutMode, setLayoutMode, toggleTheme, isDarkMode }) => {
    const navigate = useNavigate();
    
    // --- STATE MANAGEMENT ---
    const [appsMenuAnchor, setAppsMenuAnchor] = useState(null);
    const [popoverView, setPopoverView] = useState('main');
    const [directMenuAnchor, setDirectMenuAnchor] = useState(null);
    const [directMenuType, setDirectMenuType] = useState('');
    const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState('en');
    const [selectedCurrency, setSelectedCurrency] = useState('USD');

    // --- All possible actions defined in one place ---
    const allTopBarActions = useMemo(() => [
        { id: 'notifications', name: 'Notifications', icon: <Badge badgeContent={4} color="error"><NotificationsNoneOutlined /></Badge> },
        { id: 'language', name: 'Language', icon: <LanguageIcon /> },
        { id: 'currency', name: 'Currency', icon: <CurrencyIcon /> },
        { id: 'layout', name: 'Layout', icon: <LayoutIcon /> },
        { id: 'theme', name: 'Theme', icon: isDarkMode ? <LightModeIcon /> : <DarkModeIcon /> },
        { id: 'help', name: 'Help', icon: <HelpIcon /> },
    ], [isDarkMode]);

    const [topBarConfig, setTopBarConfig] = useState({ visible: [], inMenu: allTopBarActions.map(a => a.id) });

    // --- Load config from localStorage ---
    useEffect(() => {
        try {
            const savedConfig = localStorage.getItem('adminTopBarConfig');
            const allActionIds = new Set(allTopBarActions.map(a => a.id));
            if (savedConfig) {
                const parsed = JSON.parse(savedConfig);
                const savedVisible = parsed.visible.filter(id => allActionIds.has(id));
                const allSavedIds = new Set(savedVisible);
                const newInMenu = allTopBarActions.filter(a => !allSavedIds.has(a.id)).map(a => a.id);
                setTopBarConfig({ visible: savedVisible, inMenu: newInMenu });
            } else {
                setTopBarConfig({ visible: ['theme'], inMenu: allTopBarActions.filter(a => a.id !== 'theme').map(a => a.id) });
            }
        } catch (e) {
            setTopBarConfig({ visible: [], inMenu: allTopBarActions.map(a => a.id) });
        }
    }, [allTopBarActions]);
    
    // --- Mock Data ---
    const languages = [ { code: 'en', name: 'English', flag: '🇬🇧' }, { code: 'es', name: 'Español', flag: '🇪🇸' }, { code: 'fr', name: 'Français', flag: '🇫🇷' }, ];
    const currencies = [ { code: 'USD', name: 'US Dollar', symbol: '$' }, { code: 'EUR', name: 'Euro', symbol: '€' }, { code: 'INR', name: 'Indian Rupee', symbol: '₹' }, ];
    const notifications = [
        { id: 1, primary: 'New contract assigned', secondary: '2 min ago', icon: <DescriptionOutlined color="primary"/>, read: false },
        { id: 2, primary: 'License expiring soon', secondary: '1 hour ago', icon: <ReleaseNotesIcon color="warning"/>, read: false },
        { id: 3, primary: 'Server Maintenance', secondary: 'Yesterday', icon: <DnsOutlined color="info"/>, read: true },
        { id: 4, primary: 'Support ticket updated', secondary: '2 days ago', icon: <HeadsetMicOutlined color="success"/>, read: true },
    ];

    // --- Action Handlers ---
    const handleSaveTopBarConfig = (newConfig) => {
        setTopBarConfig(newConfig);
        localStorage.setItem('adminTopBarConfig', JSON.stringify(newConfig));
    };

    const handleAppsMenuOpen = (event) => setAppsMenuAnchor(event.currentTarget);
    const handleAppsMenuClose = () => {
        setAppsMenuAnchor(null);
        setTimeout(() => setPopoverView('main'), 300);
    };

    const handleDirectMenuOpen = (event, type) => {
        setDirectMenuAnchor(event.currentTarget);
        setDirectMenuType(type);
    };

    const handleDirectMenuClose = () => {
        setDirectMenuAnchor(null);
        setDirectMenuType('');
    };

    const handleItemClick = (item, event) => {
        const menuItems = ['notifications', 'language', 'currency', 'help'];
        if (menuItems.includes(item.id)) {
            handleDirectMenuOpen(event, item.id);
        } else if (item.id === 'layout') {
            setLayoutMode(p => p === 'topbar' ? 'sidebar' : 'topbar');
        } else if (item.id === 'theme') {
            toggleTheme();
        }
    };
    
    const handleFlyoutItemClick = (item) => {
         const menuItems = ['notifications', 'language', 'currency', 'help'];
        if (menuItems.includes(item.id)) {
            setPopoverView(item.id);
        } else if (item.id === 'layout') {
            setLayoutMode(p => p === 'topbar' ? 'sidebar' : 'topbar');
            handleAppsMenuClose();
        } else if (item.id === 'theme') {
            toggleTheme();
            handleAppsMenuClose();
        }
    };

    const allItemsMap = new Map(allTopBarActions.map(i => [i.id, i]));
    const visibleItems = topBarConfig.visible.map(id => allItemsMap.get(id)).filter(Boolean);
    const inMenuItems = topBarConfig.inMenu.map(id => allItemsMap.get(id)).filter(Boolean);

    // --- Renders Popover Content (both for main flyout and direct menus) ---
    const renderPopoverContent = (isDirectMenu = false) => {
        const viewType = isDirectMenu ? directMenuType : popoverView;
        const closeHandler = isDirectMenu ? handleDirectMenuClose : () => setPopoverView('main');

        const PopoverHeader = ({ title }) => (
            <AdminComponents.PopoverHeaderBox>
                <IconButton size="small" onClick={() => setPopoverView('main')}><BackIcon fontSize="small" /></IconButton>
                <AdminComponents.PopoverHeaderTitle>{title}</AdminComponents.PopoverHeaderTitle>
            </AdminComponents.PopoverHeaderBox>
        );
        
        switch (viewType) {
            case 'language': return (<Box>{!isDirectMenu && <PopoverHeader title="Language" />}<List dense subheader={isDirectMenu && <ListSubheader>Language</ListSubheader>}>{languages.map(l => (<MenuItem key={l.code} onClick={() => { setSelectedLanguage(l.code); closeHandler(); }}><ListItemIcon>{l.flag}</ListItemIcon><ListItemText primary={l.name} />{selectedLanguage === l.code && <Check color="primary" />}</MenuItem>))}</List></Box>);
            case 'currency': return (<Box>{!isDirectMenu && <PopoverHeader title="Currency" />}<List dense subheader={isDirectMenu && <ListSubheader>Currency</ListSubheader>}>{currencies.map(c => (<MenuItem key={c.code} onClick={() => { setSelectedCurrency(c.code); closeHandler(); }}><ListItemIcon>{c.symbol}</ListItemIcon><ListItemText primary={c.name} />{selectedCurrency === c.code && <Check color="primary" />}</MenuItem>))}</List></Box>);
            case 'notifications': return (<Box>{!isDirectMenu && <PopoverHeader title="Notifications" />}<AdminComponents.NotificationListStyled subheader={isDirectMenu && <ListSubheader>Notifications</ListSubheader>}>{notifications.map(n => (<AdminComponents.NotificationListItem key={n.id} divider button><ListItemIcon>{n.icon}</ListItemIcon><ListItemText primary={n.primary} secondary={n.secondary} />{!n.read && <Box className="unread-indicator"/>}</AdminComponents.NotificationListItem>))}</AdminComponents.NotificationListStyled><AdminComponents.ViewAllButtonContainer><Button size="small" onClick={closeHandler}>View All</Button></AdminComponents.ViewAllButtonContainer></Box>);
            case 'help': return (<Box>{!isDirectMenu && <PopoverHeader title="Help" />}<List dense subheader={isDirectMenu && <ListSubheader>Help & Resources</ListSubheader>}><MenuItem onClick={closeHandler}><ListItemIcon><UserManualIcon/></ListItemIcon><ListItemText primary="User Manual"/></MenuItem><MenuItem onClick={closeHandler}><ListItemIcon><ReleaseNotesIcon/></ListItemIcon><ListItemText primary="Release Notes"/></MenuItem><AdminComponents.StyledDivider /><MenuItem onClick={closeHandler}><ListItemIcon><ContactSupportOutlined/></ListItemIcon><ListItemText primary="Contact Support"/></MenuItem></List></Box>);
            default: 
                return (<Box>
                    <Grid container spacing={1}>
                        {inMenuItems.map(item => (<Grid xs={4} key={item.id}><AdminComponents.AppsGridMenuItem onClick={() => handleFlyoutItemClick(item)}>{item.icon}<Typography variant="caption" textAlign="center">{item.name}</Typography></AdminComponents.AppsGridMenuItem></Grid>))}
                    </Grid>
                    <AdminComponents.StyledDivider />
                    <MenuItem onClick={() => { handleAppsMenuClose(); setIsConfigureModalOpen(true); }}>
                        <ListItemIcon><SettingsIcon fontSize="small" /></ListItemIcon>
                        <ListItemText>Customize Top Bar</ListItemText>
                    </MenuItem>
                </Box>);
        }
    }

    return (
        <>
            <AdminComponents.TopBarAppBar position="sticky" elevation={0}>
                <AdminComponents.TopBarToolbar>
                    <AdminComponents.TopBarLeftSection><Logo /></AdminComponents.TopBarLeftSection>
                    {/* FIX: Replaced local styled components with versions from AdminComponents */}
                    <AdminComponents.TopBarCenterSection>
                        <AdminComponents.TopBarSearch>
                            <AdminComponents.TopBarSearchIconWrapper>
                                <SearchIcon />
                            </AdminComponents.TopBarSearchIconWrapper>
                            <AdminComponents.TopBarStyledInputBase placeholder="Search anything..." />
                        </AdminComponents.TopBarSearch>
                    </AdminComponents.TopBarCenterSection>
                    <AdminComponents.TopBarRightSection>
                        
                        {visibleItems.map(item => (
                            <Tooltip title={item.name} key={item.id}>
                                <IconButton color="inherit" onClick={(e) => handleItemClick(item, e)}>
                                    {item.icon}
                                </IconButton>
                            </Tooltip>
                        ))}
                        
                        <Tooltip title="More">
                            <IconButton color="inherit" onClick={handleAppsMenuOpen}>
                                <AppsIcon />
                            </IconButton>
                        </Tooltip>
                        
                        <AdminComponents.VerticalDivider orientation="vertical" flexItem />
                        <AdminComponents.TopBarUserButton onClick={() => navigate('/admin/panel')} color="inherit">
                            <AdminComponents.TopBarUserAvatar src={userIcon} />
                            <Typography>Admin</Typography>
                        </AdminComponents.TopBarUserButton>
                        <Tooltip title="Logout"><IconButton color="error" onClick={logout}><LogoutIcon /></IconButton></Tooltip>
                    </AdminComponents.TopBarRightSection>
                </AdminComponents.TopBarToolbar>
            </AdminComponents.TopBarAppBar>
            
            <AdminComponents.TopBarAppsMenu
                anchorEl={appsMenuAnchor}
                open={Boolean(appsMenuAnchor)}
                onClose={handleAppsMenuClose}
                popoverView={popoverView}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
                {renderPopoverContent(false)}
            </AdminComponents.TopBarAppsMenu>


            <AdminComponents.DirectMenuPopover anchorEl={directMenuAnchor} open={Boolean(directMenuAnchor)} onClose={handleDirectMenuClose} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} transformOrigin={{ vertical: 'top', horizontal: 'right' }}>
                {renderPopoverContent(true)}
            </AdminComponents.DirectMenuPopover>

            <ConfigureTopBarModal open={isConfigureModalOpen} onClose={() => setIsConfigureModalOpen(false)} config={topBarConfig} onSave={handleSaveTopBarConfig} allItems={allTopBarActions}/>
        </>
    );
};

export default TopBar;