import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, ToggleButton, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    MoreVert, Close, SearchOff, Settings, Schedule, <PERSON><PERSON>hart, <PERSON><PERSON>hart, <PERSON><PERSON>hart, DonutLarge
} from '@mui/icons-material';
import theme, { AdminComponents } from '../../styles/theme';

// --- GENERIC ACTIVITY LOG ---
const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Recent Activity
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <List disablePadding>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <ListItemText
                            primary={
                                <AdminComponents.ActivityLogTextContainer>
                                    <Typography variant="body2" component="span" color="text.secondary">
                                        <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                        {' '}{log.action}{' '}
                                        {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {log.timestamp}
                                    </Typography>
                                </AdminComponents.ActivityLogTextContainer>
                            }
                        />
                    </AdminComponents.ActivityLogListItem>
                ))}
            </List>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);


// --- GENERIC ACTION BUTTONS ---
const ActionButtons = ({ item, onView, onEdit, onDelete, isCondensed }) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = (event) => {
        event.stopPropagation();
        setAnchorEl(null);
    };

    const handleAction = (action, event) => {
        handleClose(event);
        action();
    }

    if (isCondensed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton aria-label="more" onClick={handleClick} size="small">
                    <MoreVert />
                </IconButton>
                <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onView(item, false), e)}>
                        <Visibility fontSize="small" /> View
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onEdit(item, true), e)}>
                        <Edit fontSize="small" /> Edit
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ErrorMenuItem onClick={(e) => handleAction(() => onDelete([item.id]), e)}>
                        <Delete fontSize="small" /> Delete
                    </AdminComponents.ErrorMenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(item, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onEdit(item, true)} title="Edit"><Edit fontSize="small" /></IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([item.id])} title="Delete"><Delete fontSize="small" /></IconButton>
        </Box>
    );
};

// --- GENERIC DATA TABLE ---
const DataTable = ({ items, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, onDelete, onEdit, onView, isCondensed, config }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (item, colKey) => {
        if (config.renderers?.tableCell) {
            const customRender = config.renderers.tableCell(item, colKey);
            if (customRender) return customRender;
        }

        const value = colKey.includes('.') ? colKey.split('.').reduce((o, i) => o?.[i], item) : item[colKey];
        const primaryKey = config.columns.find(c => c.isPrimary)?.key || 'name';

        if (colKey === primaryKey) {
            return <AdminComponents.ClickableTypography component="span" onClick={() => onRowClick(item)}>{value}</AdminComponents.ClickableTypography>;
        }
        return value || '-';
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    const renderGroupedRows = (data, keys, level = 0) => {
        if (!keys.length || !data.length) {
            return data.map(item => (
                <TableRow key={item.id} hover selected={selectedId === item.id} onClick={() => onRowClick(item)}>
                    <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(item.id)} onChange={() => onSelectOne(item.id)} onClick={e => e.stopPropagation()} /></TableCell>
                    {columnOrder.map(colKey => (
                        <AdminComponents.ContentTableCell key={colKey}>{renderCellContent(item, colKey)}</AdminComponents.ContentTableCell>
                    ))}
                    <AdminComponents.ActionTableCell align="center"><ActionButtons item={item} onView={onView} onEdit={onEdit} onDelete={onDelete} isCondensed={isCondensed} /></AdminComponents.ActionTableCell>
                </TableRow>
            ));
        }

        const currentKey = keys[0];
        const remainingKeys = keys.slice(1);
        const groupLabel = config.columns.find(c => c.key === currentKey)?.label;

        const grouped = data.reduce((acc, item) => {
            const groupValue = String(item[currentKey] || 'N/A');
            if (!acc[groupValue]) acc[groupValue] = [];
            acc[groupValue].push(item);
            return acc;
        }, {});

        return Object.entries(grouped).map(([groupValue, items]) => (
            <React.Fragment key={`${level}-${groupValue}`}>
                <AdminComponents.GroupHeaderRow>
                    <AdminComponents.GroupHeaderCell colSpan={columnOrder.length + 2} style={{ paddingLeft: theme.spacing(2 + level * 2) }}>
                        <strong>{groupLabel}:</strong> {groupValue} ({items.length})
                    </AdminComponents.GroupHeaderCell>
                </AdminComponents.GroupHeaderRow>
                {renderGroupedRows(items, remainingKeys, level + 1)}
            </React.Fragment>
        ));
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < items.length} checked={items.length > 0 && selectedIds.length === items.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell key={colKey} draggable onDragStart={() => (dragItemIndex.current = index)} onDragEnter={() => (dragOverItemIndex.current = index)} onDrop={handleDrop} onDragOver={(e) => e.preventDefault()} sortDirection={sortColumn === colKey ? sortDirection : false}>
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {config.columns.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>{renderGroupedRows(items, groupByKeys)}</TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

// --- MAIN TEMPLATE COMPONENT ---
const PageTemplate = ({ pageConfig }) => {
    const {
        initialData = [], columns = [], summaryCards = [], mainActions = [], renderers = {},
        activityLog: initialActivityLog = [],
        filterOperators = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'],
        singularNoun = 'Item', pluralNoun = 'Items',
    } = pageConfig;

    const [items, setItems] = useState(initialData);
    const [selectedItem, setSelectedItem] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, item: null, isAdding: false });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [activityLog, setActivityLog] = useState(initialActivityLog);
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState(columns.find(c => c.isPrimary)?.key || 'name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isDetailsPaneVisible, setIsDetailsPaneVisible] = useState(false);
    const [columnOrder, setColumnOrder] = useState(columns.map(c => c.key));
    const [summaryFilter, setSummaryFilter] = useState(null);
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [chartType, setChartType] = useState('bar');

    const quickFilterOptions = useMemo(() => {
        const quickFilterColumns = columns.filter(c => c.isQuickFilter).map(c => c.key);
        const options = new Set();
        items.forEach(item => {
            quickFilterColumns.forEach(key => {
                if (item[key]) options.add(item[key]);
            });
        });
        return [...options];
    }, [items, columns]);

    const processedItems = useMemo(() => {
        let current = items.filter(c => !c.isDraft);
        if (summaryFilter) {
            current = current.filter(c => c[summaryFilter.key] === summaryFilter.value);
        }
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(item => columns.some(col => String(col.key.includes('.') ? col.key.split('.').reduce((o, i) => o?.[i], item) : item[col.key]).toLowerCase().includes(term)));
        }
        if (activeFilters.length > 0) {
            current = current.filter(item => activeFilters.every(filter => {
                const itemValue = String(filter.field.includes('.') ? filter.field.split('.').reduce((o, i) => o?.[i], item) : item[filter.field]).toLowerCase();
                const filterValue = String(filter.value).toLowerCase();
                switch (filter.operator) {
                    case 'Equals': return itemValue === filterValue;
                    case 'Not Equals': return itemValue !== filterValue;
                    case 'Contains': return itemValue.includes(filterValue);
                    case 'Starts With': return itemValue.startsWith(filterValue);
                    case 'Ends With': return itemValue.endsWith(filterValue);
                    default: return true;
                }
            }));
        }
        return [...current].sort((a, b) => {
            const valA = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], a) : a[sortColumn];
            const valB = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], b) : b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? (valA || 0) - (valB || 0) : (valB || 0) - (valA || 0);
        });
    }, [items, searchTerm, activeFilters, sortColumn, sortDirection, summaryFilter, columns]);

    const displayItem = useMemo(() => {
        const isSelectedVisible = processedItems.some(c => c.id === selectedItem?.id);
        if (isSelectedVisible) return selectedItem;
        return processedItems.length > 0 ? processedItems[0] : null;
    }, [processedItems, selectedItem]);

    const addLog = (logEntry) => setActivityLog(prev => [{ ...logEntry, timestamp: new Date().toLocaleString() }, ...prev].slice(0, 10));
    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const confirmDelete = () => {
        setItems(prev => prev.filter(item => !deleteConfirmation.idsToDelete.includes(item.id)));
        addLog({ user: 'Admin', action: `Deleted ${deleteConfirmation.idsToDelete.length} ${singularNoun}(s).` });
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
        setSelectedIds([]);
    };
    const handleShowDetails = (item, isEditing = false) => setModalState({ isOpen: true, item, isAdding: isEditing });
    const handleShowAddModal = () => setModalState({ isOpen: true, item: { id: Date.now(), isDraft: true }, isAdding: true });
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedItems.map(c => c.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) setIsDetailsPaneVisible(false);
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) newOrder = columnOrder.filter(key => key !== columnKey);
            else return;
        } else {
            const originalKeys = columns.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
        addLog({ user: 'Admin', action: `Toggled '${columns.find(c => c.key === columnKey)?.label || columnKey}' column visibility` });
    };
    const handleDetailsPaneToggle = () => {
        setIsDetailsPaneVisible(prev => !prev);
        setIsSidebarOpen(false);
    };
    const summaryStats = useMemo(() => {
        const stats = { total: items.length };
        summaryCards.forEach(card => {
            if (!card.isTotal) stats[card.value] = items.filter(item => item[card.key] === card.value).length;
        });
        return stats;
    }, [items, summaryCards]);
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleAddQuickFilter = (value) => {
        const column = columns.find(c => new Set(items.map(i => i[c.key])).has(value));
        if (column) setStagedFilters([...stagedFilters, { field: column.key, operator: 'Equals', value, id: Date.now() }]);
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setSummaryFilter(null);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        setSummaryFilter(null);
    };
    const handleSummaryCardClick = (card) => {
        const newFilter = card && !card.isTotal ? { key: card.key, value: card.value } : null;
        setSummaryFilter(prev => JSON.stringify(prev) === JSON.stringify(newFilter) ? null : newFilter);
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };
    const handleGroupByChange = (key) => setGroupByKeys(prev => prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]);

    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isDetailsPaneVisible;
        const handlers = { onSelect: handleSelectOne, onDelete: handleDeleteRequest, onEdit: handleShowDetails, onView: handleShowDetails };

        const renderMappedComponent = (renderer, item) => {
            const props = { ...handlers, isSelected: displayItem?.id === item.id, isChecked: selectedIds.includes(item.id), onClick: () => setSelectedItem(item), isCondensed };
            return React.cloneElement(renderer(item, props), { key: item.id });
        };

        return (
            <AdminComponents.ViewContainer>
                {processedItems.length > 0 ? (
                    <>
                        {viewMode === 'cards' && renderers.card && <AdminComponents.GridView>{processedItems.map(item => renderMappedComponent(renderers.card, item))}</AdminComponents.GridView>}
                        {viewMode === 'grid' && <DataTable items={processedItems} onRowClick={setSelectedItem} onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }} sortColumn={sortColumn} sortDirection={sortDirection} selectedId={displayItem?.id} selectedIds={selectedIds} onSelectAll={handleSelectAll} onSelectOne={handleSelectOne} columnOrder={columnOrder} setColumnOrder={setColumnOrder} addLog={addLog} groupByKeys={groupByKeys} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={isCondensed} config={pageConfig} />}
                        {viewMode === 'compact' && renderers.compactCard && <AdminComponents.CompactView>{processedItems.map(item => renderMappedComponent(renderers.compactCard, item))}</AdminComponents.CompactView>}
                        {viewMode === 'list' && renderers.listItem && <AdminComponents.ListView>{processedItems.map(item => renderMappedComponent(renderers.listItem, item))}</AdminComponents.ListView>}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching {pluralNoun}</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === null} onClick={() => handleSummaryCardClick(null)}>
                                        <AdminComponents.SummaryAvatar variant="total">{summaryCards.find(c => c.isTotal)?.icon || <Apps />}</AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total {pluralNoun}</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    {summaryCards.filter(c => !c.isTotal).map(card => (
                                        <AdminComponents.SummaryCard key={card.value} isActive={summaryFilter?.value === card.value} onClick={() => handleSummaryCardClick(card)}>
                                            <AdminComponents.SummaryAvatar variant={card.value.toLowerCase()}>{card.icon}</AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats[card.value]}</Typography><Typography variant="body2">{card.value}</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                    ))}
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    {mainActions.map(action => (
                                        <Button
                                            key={action.label}
                                            variant={action.action === 'toggleDetails' ? (isDetailsPaneVisible ? 'contained' : 'outlined') : (action.variant || 'contained')}
                                            startIcon={action.icon}
                                            onClick={action.action === 'add' ? handleShowAddModal : handleDetailsPaneToggle}
                                        >
                                            {action.label}
                                        </Button>
                                    ))}
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedItems.length > 0 && selectedIds.length === processedItems.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedItems.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<Settings />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup size="small" value={viewMode} exclusive onChange={(e, v) => v && setViewMode(v)}>
                                    {renderers.card && <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>}
                                    {renderers.compactCard && <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>}
                                    {renderers.listItem && <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>}
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            {renderers.detailsPane && (
                                <AdminComponents.DetailsPane isCollapsed={!isDetailsPaneVisible}>
                                    <AdminComponents.ChartTypeSelectorContainer>
                                        <AdminComponents.StyledToggleButtonGroup size="small" value={chartType} exclusive onChange={(e, v) => v && setChartType(v)} fullWidth>
                                            <ToggleButton value="bar"><BarChart />Bar</ToggleButton>
                                            <ToggleButton value="line"><ShowChart />Line</ToggleButton>
                                            <ToggleButton value="pie"><PieChart />Pie</ToggleButton>
                                            <ToggleButton value="doughnut"><DonutLarge />Donut</ToggleButton>
                                        </AdminComponents.StyledToggleButtonGroup>
                                    </AdminComponents.ChartTypeSelectorContainer>
                                    {renderers.detailsPane(displayItem, chartType)}
                                </AdminComponents.DetailsPane>
                            )}
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                <Drawer variant="persistent" anchor="right" open={isSidebarOpen}>
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">{sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}</Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}><Close /></IconButton>
                        </AdminComponents.SidebarHeader>
                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.QuickFilterContainer>{quickFilterOptions.map(opt => <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />)}</AdminComponents.QuickFilterContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Field</InputLabel>
                                            <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>{columns.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}</Select>
                                        </FormControl>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Operator</InputLabel>
                                            <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>{filterOperators.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}</Select>
                                        </FormControl>
                                        <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                                        <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {activeFilters.length > 0 ? activeFilters.map(f => <Chip key={f.id} label={`${columns.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnVisibilityContainer>{columns.map(col => <FormControlLabel key={col.key} control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} />} label={col.label} />)}</AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnVisibilityContainer>{columns.filter(c => c.groupable).map(col => <FormControlLabel key={col.key} control={<Checkbox checked={groupByKeys.includes(col.key)} onChange={() => handleGroupByChange(col.key)} />} label={col.label} />)}</AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                        </AdminComponents.SidebarContent>
                        <AdminComponents.SidebarFooter>
                            {sidebarMode === 'search' && (<><Button variant="outlined" onClick={handleResetFilters}>Reset</Button><Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button></>)}
                            {sidebarMode === 'grid' && (<Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>)}
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>
                {renderers.addEditModal && modalState.isOpen && renderers.addEditModal(modalState, setModalState, setItems)}
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default PageTemplate;
